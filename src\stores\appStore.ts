/**
 * 应用配置状态管理
 * 管理主题、语言、全局配置等
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import React from "react";
import { useColorScheme } from "react-native";
import { create } from "zustand";
import { API_CONFIG, STORAGE_KEYS } from "../constants";
import { AppConfig, Language, ThemeMode } from "../types";

// 按照Medium文章和Expo文档：不需要复杂的主题计算函数
// 主题计算直接在useTheme hook中进行

interface AppState {
  // 主题配置 - 按照Medium文章：只存储主题模式
  theme: ThemeMode;

  // 语言配置
  language: Language;

  // 应用配置
  config: AppConfig;

  // 全局状态
  isInitialized: boolean;
  isOnline: boolean;

  // 操作方法
  setTheme: (theme: ThemeMode) => Promise<void>;
  toggleTheme: () => Promise<void>;
  setLanguage: (language: Language) => Promise<void>;
  setOnlineStatus: (isOnline: boolean) => void;
  initializeApp: () => Promise<void>;
  updateConfig: (config: Partial<AppConfig>) => Promise<void>;

  // 内部方法
  loadStoredConfig: () => Promise<void>;
  saveConfig: () => Promise<void>;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态 - 按照Medium文章的方案
  theme: "light",
  language: "zh-CN",
  config: {
    theme: "light",
    language: "zh-CN",
    apiUrl: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
  },
  isInitialized: false,
  isOnline: true,

  // 设置主题 - 完全按照Medium文章的方案
  setTheme: async (theme: ThemeMode) => {
    try {
      // 按照Medium文章：只保存主题模式，不计算isDarkMode
      set({
        theme,
        config: { ...get().config, theme },
      });

      // 按照Medium文章：只保存主题模式到AsyncStorage
      await AsyncStorage.setItem(
        STORAGE_KEYS.THEME_CONFIG,
        JSON.stringify({ theme })
      );

      await get().saveConfig();
    } catch (error) {
      console.error("Failed to set theme:", error);
    }
  },

  // 切换主题 (循环切换: light -> dark -> system -> light)
  toggleTheme: async () => {
    const { theme, setTheme } = get();
    let newTheme: ThemeMode;
    switch (theme) {
      case "light":
        newTheme = "dark";
        break;
      case "dark":
        newTheme = "system";
        break;
      case "system":
        newTheme = "light";
        break;
      default:
        newTheme = "light";
    }
    await setTheme(newTheme);
  },

  // 设置语言
  setLanguage: async (language: Language) => {
    try {
      set({
        language,
        config: { ...get().config, language },
      });

      await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
      await get().saveConfig();
    } catch (error) {
      console.error("Failed to set language:", error);
    }
  },

  // 设置网络状态
  setOnlineStatus: (isOnline: boolean) => {
    set({ isOnline });
  },

  // 更新配置
  updateConfig: async (newConfig: Partial<AppConfig>) => {
    try {
      const currentConfig = get().config;
      const updatedConfig = { ...currentConfig, ...newConfig };

      set({ config: updatedConfig });
      await get().saveConfig();
    } catch (error) {
      console.error("Failed to update config:", error);
    }
  },

  // 加载存储的配置 - 按照Medium文章的方案
  loadStoredConfig: async () => {
    try {
      // 加载主题配置
      const themeConfigStr = await AsyncStorage.getItem(
        STORAGE_KEYS.THEME_CONFIG
      );
      if (themeConfigStr) {
        const themeConfig = JSON.parse(themeConfigStr);
        const theme = themeConfig.theme || "light";
        // 按照Medium文章：只设置主题模式，不设置isDarkMode
        set({ theme });
      } else {
        // 按照Medium文章：如果没有保存的主题，使用系统主题作为默认值
        // 但这里我们先设置为light，让useTheme hook来处理系统主题
        set({ theme: "light" });
      }

      // 加载语言配置
      const language = (await AsyncStorage.getItem(
        STORAGE_KEYS.LANGUAGE
      )) as Language;
      if (language) {
        set({ language });
      }

      // 加载完整配置
      const configStr = await AsyncStorage.getItem("appConfig");
      if (configStr) {
        const config = JSON.parse(configStr);
        set({ config: { ...get().config, ...config } });
      }
    } catch (error) {
      console.error("Failed to load stored config:", error);
    }
  },

  // 保存配置
  saveConfig: async () => {
    try {
      const { config } = get();
      await AsyncStorage.setItem("appConfig", JSON.stringify(config));
    } catch (error) {
      console.error("Failed to save config:", error);
    }
  },

  // 初始化应用
  initializeApp: async () => {
    try {
      set({ isInitialized: false });

      // 加载存储的配置
      await get().loadStoredConfig();

      // 这里可以添加其他初始化逻辑
      // 比如检查应用更新、初始化第三方SDK等

      set({ isInitialized: true });
    } catch (error) {
      console.error("Failed to initialize app:", error);
      set({ isInitialized: true }); // 即使失败也要设置为已初始化
    }
  },


}));

// 导出主题相关的选择器 - 完全按照Medium文章和Expo官方文档的方案
export const useTheme = () => {
  const { theme: themeMode, setTheme, toggleTheme } = useAppStore();
  const { getTheme } = require("../components/design-system/theme");

  // 按照Expo官方文档：直接使用useColorScheme检测系统主题
  const systemColorScheme = useColorScheme();

  // 按照Medium文章：计算最终的主题
  const finalTheme = React.useMemo(() => {
    if (themeMode === 'system') {
      // 按照Expo文档：直接使用systemColorScheme的值
      return systemColorScheme || 'light';
    }
    return themeMode;
  }, [themeMode, systemColorScheme]);

  // 计算isDarkMode - 基于最终主题
  const computedIsDarkMode = finalTheme === 'dark';

  // 获取主题对象
  const theme = getTheme(computedIsDarkMode);

  return {
    theme,
    themeMode,
    isDarkMode: computedIsDarkMode,
    setTheme,
    toggleTheme,
    systemColorScheme // 暴露系统主题供调试使用
  };
};

// 导出语言相关的选择器
export const useLanguage = () => {
  const { language, setLanguage } = useAppStore();
  return { language, setLanguage };
};

// 导出网络状态选择器
export const useNetworkStatus = () => {
  const { isOnline, setOnlineStatus } = useAppStore();
  return { isOnline, setOnlineStatus };
};
