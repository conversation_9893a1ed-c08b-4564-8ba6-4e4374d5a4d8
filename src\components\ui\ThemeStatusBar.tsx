/**
 * ThemeStatusBar 组件
 * 根据当前主题自动调整StatusBar样式的统一管理组件
 * 基于Expo官方文档和最佳实践实现
 */

import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { Platform } from 'react-native';
import { useTheme } from '../../stores/appStore';

interface ThemeStatusBarProps {
  /**
   * 强制指定StatusBar样式，覆盖主题自动判断
   */
  style?: 'light' | 'dark' | 'auto';
  /**
   * 是否隐藏StatusBar
   */
  hidden?: boolean;
  /**
   * StatusBar背景色（仅Android）
   */
  backgroundColor?: string;
  /**
   * 是否半透明（仅Android）
   */
  translucent?: boolean;
  /**
   * 动画类型（仅iOS）
   */
  animated?: boolean;
}

/**
 * 根据主题自动调整StatusBar样式的组件
 * 
 * 使用方式：
 * ```tsx
 * // 自动根据主题调整
 * <ThemeStatusBar />
 * 
 * // 强制指定样式
 * <ThemeStatusBar style="dark" />
 * 
 * // 自定义背景色
 * <ThemeStatusBar backgroundColor="#000000" />
 * ```
 */
export const ThemeStatusBar: React.FC<ThemeStatusBarProps> = ({
  style,
  hidden = false,
  backgroundColor,
  translucent = false,
  animated = true,
}) => {
  const { theme, isDarkMode } = useTheme();

  // 获取StatusBar样式
  const getStatusBarStyle = (): 'light' | 'dark' | 'auto' => {
    if (style) {
      return style;
    }

    // 根据主题自动判断
    if (isDarkMode) {
      return 'light'; // 暗色主题使用浅色状态栏内容
    } else {
      return 'dark'; // 浅色主题使用深色状态栏内容
    }
  };

  // 获取背景色
  const getBackgroundColor = (): string | undefined => {
    if (backgroundColor) {
      return backgroundColor;
    }

    // 仅在Android上设置背景色
    if (Platform.OS === 'android') {
      return theme.colors.background.primary;
    }

    return undefined;
  };

  return (
    <StatusBar
      style={getStatusBarStyle()}
      hidden={hidden}
      backgroundColor={getBackgroundColor()}
      translucent={translucent}
      animated={animated}
    />
  );
};

/**
 * 预设的StatusBar配置
 */
export const StatusBarPresets = {
  /**
   * 默认配置 - 跟随主题
   */
  default: () => <ThemeStatusBar />,
  
  /**
   * 浅色配置 - 强制浅色内容
   */
  light: () => <ThemeStatusBar style="light" />,
  
  /**
   * 深色配置 - 强制深色内容
   */
  dark: () => <ThemeStatusBar style="dark" />,
  
  /**
   * 隐藏配置
   */
  hidden: () => <ThemeStatusBar hidden />,
  
  /**
   * 透明配置（Android）
   */
  transparent: () => (
    <ThemeStatusBar 
      backgroundColor="transparent" 
      translucent 
    />
  ),
};

export default ThemeStatusBar;
