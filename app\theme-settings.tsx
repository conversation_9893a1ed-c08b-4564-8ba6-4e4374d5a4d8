/**
 * Theme Settings Screen
 * Allows the user to select the app theme.
 * Supports responsive layout for landscape mode.
 */
import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Screen, Text, Card, Container } from '../src/components/ui';
import { useTheme } from '../src/stores/appStore';

const ThemeSettingsScreen = () => {
  const { theme, themeMode, setTheme } = useTheme();
  const router = useRouter();

  // 屏幕方向和尺寸状态
  const [screenData, setScreenData] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return {
      width,
      height,
      isLandscape: width > height,
      isTablet: Math.min(width, height) >= 600, // 判断是否为平板设备
    };
  });

  const themeOptions = [
    { value: 'light', label: '浅色' },
    { value: 'dark', label: '深色' },
    { value: 'system', label: '跟随系统' },
  ];

  // 监听屏幕方向变化
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({
        width: window.width,
        height: window.height,
        isLandscape: window.width > window.height,
        isTablet: Math.min(window.width, window.height) >= 600,
      });
    });

    return () => subscription?.remove();
  }, []);

  const handleThemeSelect = async (selectedTheme: 'light' | 'dark' | 'system') => {
    await setTheme(selectedTheme);
    // Vibrate or give some feedback if you want
    // Optionally navigate back
    if (router.canGoBack()) {
      router.back();
    }
  };

  // 获取响应式容器样式
  const getResponsiveContainerStyle = () => {
    if (screenData.isLandscape && screenData.isTablet) {
      // 横屏平板模式：类似iPad设置页面的布局
      return {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
      };
    } else if (screenData.isLandscape) {
      // 横屏手机模式：限制宽度并居中
      return {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
      };
    } else {
      // 竖屏模式：保持原有样式
      return {
        flex: 1,
        paddingHorizontal: 24,
        paddingTop: 20,
      };
    }
  };

  // 获取卡片样式
  const getCardStyle = () => {
    if (screenData.isLandscape) {
      return {
        maxWidth: screenData.isTablet ? 500 : 400,
        width: '100%',
        elevation: screenData.isTablet ? 4 : 2,
      };
    } else {
      return {
        elevation: 2,
      };
    }
  };

  return (
    <Screen backgroundColor={theme.colors.background.secondary}>
      <Stack.Screen
        options={{
          title: '主题设置',
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTintColor: theme.colors.text.primary,
          headerTitleStyle: {
            color: theme.colors.text.primary,
          }
        }}
      />
      <View style={getResponsiveContainerStyle()}>
        <Card
          padding={6}
          style={getCardStyle()}
        >
          {/* 横屏模式下添加标题 */}
          {screenData.isLandscape && (
            <View style={styles.landscapeHeader}>
              <Text variant="h5" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
                选择主题
              </Text>
            </View>
          )}

          {themeOptions.map((option, index) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionItem,
                { borderBottomColor: theme.colors.border.tertiary },
                // Remove border for the last item
                index === themeOptions.length - 1 && { borderBottomWidth: 0 },
                // 横屏模式下调整间距
                screenData.isLandscape && styles.landscapeOptionItem
              ]}
              onPress={() => handleThemeSelect(option.value as any)}
              activeOpacity={0.6}
            >
              <Text variant="body1">{option.label}</Text>
              {themeMode === option.value && (
                <FontAwesome
                  name="check"
                  size={20}
                  color={theme.colors.primary}
                />
              )}
            </TouchableOpacity>
          ))}
        </Card>
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  landscapeOptionItem: {
    paddingVertical: 20,
    paddingHorizontal: 8,
  },
  landscapeHeader: {
    alignItems: 'center',
    marginBottom: 8,
  },
});

export default ThemeSettingsScreen;