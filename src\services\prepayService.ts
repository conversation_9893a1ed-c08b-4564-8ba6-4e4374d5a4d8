/**
 * 预收款API服务
 * 复用Vue项目的后台接口
 */

import httpClient from './http';
import {
  ApiResponse,
  StoreInfo,
  BusinessPartner,
  GetBPRequest,
  CreateBPRequest,
  PrepayRequest,
  PrepayResponse,
  CreateBPResponse,
  KeywordValidateRequest,
  BpKeywordLogRequest,
  PrepayReportRequest,
  PrepayReportItem
} from '../types/prepay';

// API端点枚举（与Vue项目保持一致）
enum Api {
  BusinessPartnerList = '/api/prePay/businessPartnerList',
  PartnerList = '/api/prePay/businessPartner',
  Prepay = '/api/prePay/prepay',
  List = '/api/prePay/userOrgList',
  QrCode = '/api/prePay/qrCode/',
  KeyWordValidate = '/api/businessPartnerKeyword/keyWordValidate',
  PrepayReport = '/api/prePay/prepayReport',
  AddBpKeywordLog = '/api/bpKeywordLog/add',
}

export class PrepayService {
  /**
   * 获取门店列表
   */
  static async getStoreList(): Promise<StoreInfo[]> {
    try {
      const response = await httpClient.post<ApiResponse<StoreInfo[]>>(Api.List);
      return response.data.result || [];
    } catch (error: any) {
      console.error('获取门店列表失败:', error);
      throw new Error(error.message || '获取门店列表失败');
    }
  }

  /**
   * 获取业务伙伴列表
   */
  static async getBusinessPartnerList(params: GetBPRequest): Promise<BusinessPartner[]> {
    try {
      const response = await httpClient.post<ApiResponse<BusinessPartner[]>>(
        Api.BusinessPartnerList,
        params
      );
      return response.data.result || [];
    } catch (error: any) {
      console.error('获取业务伙伴列表失败:', error);
      throw new Error(error.message || '获取业务伙伴列表失败');
    }
  }

  /**
   * 创建业务伙伴
   */
  static async createBusinessPartner(params: CreateBPRequest): Promise<CreateBPResponse> {
    try {
      const response = await httpClient.post<ApiResponse<CreateBPResponse>>(
        Api.PartnerList,
        params
      );
      return response.data.result;
    } catch (error: any) {
      console.error('创建业务伙伴失败:', error);
      throw new Error(error.message || '创建业务伙伴失败');
    }
  }

  /**
   * 创建预付款
   */
  static async createPrepay(params: PrepayRequest): Promise<PrepayResponse> {
    try {
      const response = await httpClient.post<ApiResponse<PrepayResponse>>(
        Api.Prepay,
        params
      );
      return response.data.result;
    } catch (error: any) {
      console.error('创建预付款失败:', error);
      throw new Error(error.message || '创建预付款失败');
    }
  }

  /**
   * 获取二维码
   */
  static async getQrCode(id: string): Promise<string> {
    try {
      const response = await httpClient.post<ApiResponse<string>>(
        `${Api.QrCode}${id}`,
        { id }
      );
      return response.data.result;
    } catch (error: any) {
      console.error('获取二维码失败:', error);
      throw new Error(error.message || '获取二维码失败');
    }
  }

  /**
   * 关键字验证
   */
  static async validateKeyword(params: KeywordValidateRequest): Promise<boolean> {
    try {
      const response = await httpClient.post<ApiResponse<boolean>>(
        Api.KeyWordValidate,
        params
      );
      return response.data.result;
    } catch (error: any) {
      console.error('关键字验证失败:', error);
      throw new Error(error.message || '关键字验证失败');
    }
  }

  /**
   * 添加BP关键字日志
   */
  static async addBpKeywordLog(params: BpKeywordLogRequest): Promise<void> {
    try {
      await httpClient.post<ApiResponse<void>>(Api.AddBpKeywordLog, params);
    } catch (error: any) {
      console.error('添加BP关键字日志失败:', error);
      // 这个接口失败不影响主流程，只记录错误
    }
  }

  /**
   * 获取预收款报表
   */
  static async getPrepayReport(params: PrepayReportRequest): Promise<PrepayReportItem[]> {
    try {
      const response = await httpClient.post<ApiResponse<PrepayReportItem[]>>(
        Api.PrepayReport,
        params
      );
      return response.data.result || [];
    } catch (error: any) {
      console.error('获取预收款报表失败:', error);
      throw new Error(error.message || '获取预收款报表失败');
    }
  }

  /**
   * 获取当前日期字符串 (YYYYMMDD格式)
   */
  static getCurrentDateString(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  /**
   * 生成门店标签 - 格式：门店代码-门店名称
   */
  static getStoreLabel(store: StoreInfo): string {
    return `${store.compCode}-${store.compName}`;
  }

  /**
   * 构建预付款请求参数
   */
  static buildPrepayRequest(
    formData: GetBPRequest,
    selectedStore: StoreInfo,
    bpInfo: { kunnr: string; name1: string }
  ): PrepayRequest {
    const dateString = this.getCurrentDateString();
    let profitCenter = selectedStore.profitCenter;
    
    // 当公司代码末尾为99时，使用选择的利润中心
    if (profitCenter.slice(-2) === '99') {
      profitCenter = profitCenter.slice(0, -2) + formData.profit;
    }
    
    return {
      bukrs: selectedStore.compCode,
      newko: bpInfo.kunnr,
      wrbtr: formData.money,
      zfbdt: dateString,
      xref1: dateString,
      budat: dateString,
      prctr: profitCenter,
      BpName: bpInfo.name1,
    };
  }
}
