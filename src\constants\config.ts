/**
 * 应用配置常量
 * HuiLink移动应用的核心配置
 */

// 环境变量读取工具函数
const getEnvConfig = () => {
  // 从环境变量读取配置，支持类型转换和默认值
  const getEnvString = (key: string, defaultValue: string): string => {
    return process.env[key] || defaultValue;
  };

  const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    return value.toLowerCase() === "true";
  };

  // 从环境变量读取BASE_URL（优先级最高）
  const envBaseUrl = getEnvString("EXPO_PUBLIC_API_BASE_URL", "");
  const envDebug = process.env.EXPO_PUBLIC_DEBUG;
  const envLogLevel = getEnvString("EXPO_PUBLIC_LOG_LEVEL", "");

  return {
    baseUrl: envBaseUrl,
    debug:
      envDebug !== undefined
        ? getEnvBoolean("EXPO_PUBLIC_DEBUG", true)
        : undefined,
    logLevel: envLogLevel || undefined,
  };
};

// 获取环境变量配置
const ENV_OVERRIDES = getEnvConfig();

// 环境配置（支持环境变量覆盖）
const ENV_CONFIG = {
  development: {
    BASE_URL: ENV_OVERRIDES.baseUrl || "http://192.168.68.112:5006", // 开发环境API地址
    DEBUG: ENV_OVERRIDES.debug !== undefined ? ENV_OVERRIDES.debug : true,
    LOG_LEVEL: ENV_OVERRIDES.logLevel || "debug",
  },
  production: {
    BASE_URL: ENV_OVERRIDES.baseUrl || "https://api.huilink.com", // 生产环境API地址
    DEBUG: ENV_OVERRIDES.debug !== undefined ? ENV_OVERRIDES.debug : false,
    LOG_LEVEL: ENV_OVERRIDES.logLevel || "error",
  },
  staging: {
    BASE_URL: ENV_OVERRIDES.baseUrl || "https://staging-api.huilink.com", // 测试环境API地址
    DEBUG: ENV_OVERRIDES.debug !== undefined ? ENV_OVERRIDES.debug : true,
    LOG_LEVEL: ENV_OVERRIDES.logLevel || "info",
  },
};

// 获取当前环境
const getCurrentEnvironment = (): keyof typeof ENV_CONFIG => {
  // 方法1: 使用 __DEV__ 标志（React Native内置）
  if (__DEV__) {
    return "development";
  }

  // 方法2: 使用自定义环境变量
  const customEnv = process.env.EXPO_PUBLIC_ENV || process.env.APP_ENV;
  if (customEnv === "staging") {
    return "staging";
  }

  // 方法3: 使用标准环境变量
  if (process.env.NODE_ENV === "production") {
    return "production";
  }

  // 默认返回生产环境
  return "production";
};

// 当前环境配置
const CURRENT_ENV = getCurrentEnvironment();
const CURRENT_CONFIG = ENV_CONFIG[CURRENT_ENV];

// 环境信息（用于调试）
export const ENV_INFO = {
  current: CURRENT_ENV,
  isDevelopment: CURRENT_ENV === "development",
  isProduction: CURRENT_ENV === "production",
  isStaging: CURRENT_ENV === "staging",
  debug: CURRENT_CONFIG.DEBUG,
  logLevel: CURRENT_CONFIG.LOG_LEVEL,
};

// API配置
export const API_CONFIG = {
  // 基础API地址（根据环境自动切换）
  BASE_URL: CURRENT_CONFIG.BASE_URL,

  // API端点
  ENDPOINTS: {
    // 认证相关
    LOGIN: "/api/huiLinkAuth/login",
    CAPTCHA: "/api/sysAuth/captcha",
    USER_INFO: "/api/sysAuth/userInfo",
    LOGIN_CONFIG: "/api/sysAuth/loginConfig",
    WATERMARK_CONFIG: "/api/sysAuth/watermarkConfig",
  },

  // 请求超时时间
  TIMEOUT: 10000,

  // 请求重试次数
  RETRY_COUNT: 3,
};

// Token配置
export const TOKEN_CONFIG = {
  // Token键名
  ACCESS_TOKEN_KEY: "access-token",
  REFRESH_TOKEN_KEY: "x-access-token",

  // Token存储键名
  STORAGE_TOKEN_KEY: "token",
  STORAGE_USER_INFO_KEY: "userInfo",

  // Token过期检查间隔（毫秒）
  EXPIRE_CHECK_INTERVAL: 60000,
};

// 应用配置
export const APP_CONFIG = {
  // 应用名称
  APP_NAME: "HuiLink",

  // 版本号
  VERSION: "1.0.0",

  // 默认语言
  DEFAULT_LANGUAGE: "zh-CN",

  // 主题配置
  THEME: {
    // 主色调
    PRIMARY_COLOR: "#409EFF",
    SECONDARY_COLOR: "#F5F7FA",
    SUCCESS_COLOR: "#67C23A",
    WARNING_COLOR: "#E6A23C",
    ERROR_COLOR: "#F56C6C",
    INFO_COLOR: "#909399",

    // 文字颜色
    TEXT_PRIMARY: "#303133",
    TEXT_REGULAR: "#606266",
    TEXT_SECONDARY: "#909399",
    TEXT_PLACEHOLDER: "#C0C4CC",

    // 边框颜色
    BORDER_BASE: "#DCDFE6",
    BORDER_LIGHT: "#E4E7ED",
    BORDER_LIGHTER: "#EBEEF5",
    BORDER_EXTRA_LIGHT: "#F2F6FC",
  },

  // 动画配置
  ANIMATION: {
    DURATION_FAST: 200,
    DURATION_NORMAL: 300,
    DURATION_SLOW: 500,
  },

  // 布局配置
  LAYOUT: {
    PADDING: 16,
    MARGIN: 16,
    BORDER_RADIUS: 8,
    HEADER_HEIGHT: 60,
  },
};

// 验证配置
export const VALIDATION_CONFIG = {
  // 账号验证
  ACCOUNT: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/,
  },

  // 密码验证
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 20,
  },

  // 验证码验证
  CAPTCHA: {
    LENGTH: 4,
  },
};

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接失败，请检查网络设置",
  LOGIN_FAILED: "登录失败，请检查账号密码",
  TOKEN_EXPIRED: "登录已过期，请重新登录",
  CAPTCHA_ERROR: "验证码错误，请重新输入",
  ACCOUNT_REQUIRED: "请输入账号",
  PASSWORD_REQUIRED: "请输入密码",
  CAPTCHA_REQUIRED: "请输入验证码",
  ACCOUNT_FORMAT_ERROR: "账号格式不正确",
  PASSWORD_FORMAT_ERROR: "密码长度应为6-20位",
  UNKNOWN_ERROR: "未知错误，请稍后重试",
};

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: "登录成功",
  LOGOUT_SUCCESS: "退出成功",
};

// 环境相关工具函数
export const EnvUtils = {
  /**
   * 获取当前环境名称
   */
  getCurrentEnv: () => CURRENT_ENV,

  /**
   * 检查是否为开发环境
   */
  isDev: () => ENV_INFO.isDevelopment,

  /**
   * 检查是否为生产环境
   */
  isProd: () => ENV_INFO.isProduction,

  /**
   * 检查是否为测试环境
   */
  isStaging: () => ENV_INFO.isStaging,

  /**
   * 获取当前环境的完整配置
   */
  getConfig: () => CURRENT_CONFIG,

  /**
   * 条件执行函数（仅在开发环境）
   */
  devOnly: (fn: () => void) => {
    if (ENV_INFO.isDevelopment) {
      fn();
    }
  },

  /**
   * 条件执行函数（仅在生产环境）
   */
  prodOnly: (fn: () => void) => {
    if (ENV_INFO.isProduction) {
      fn();
    }
  },

  /**
   * 环境日志（根据环境配置决定是否输出）
   */
  log: (
    message: string,
    level: "debug" | "info" | "warn" | "error" = "info"
  ) => {
    if (!CURRENT_CONFIG.DEBUG) return;

    const logLevels = { debug: 0, info: 1, warn: 2, error: 3 };
    const currentLevel =
      logLevels[CURRENT_CONFIG.LOG_LEVEL as keyof typeof logLevels] || 1;
    const messageLevel = logLevels[level];

    if (messageLevel >= currentLevel) {
      const prefix = `[${CURRENT_ENV.toUpperCase()}]`;
      console[level](`${prefix} ${message}`);
    }
  },
};

// 帆软系统配置
export const FANRUAN_CONFIG = {
  development: {
    SERVER_URL: "https://datacenter.chinagrandauto.com:9443/", // 开发环境帆软地址
    PROJECT_NAME: "webroot", // 工程名
    USE_ENCRYPTION: true, // 使用加密方案
    ENABLE_TIMEOUT: false, // 是否启用超时
    PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlhUxCHnUXdUi/N6R3+GWUF171HtLNmCy
y2g1w/HVE0dabBgZJlj+NoFxeiPJBFOfZWReLRF/77HxT+1DJls81H/weHOWvgBidUmuhsWnAzOy
5rZ8IykV9rPeAjMU+mZYujD4P17yPeGfyU02hn/EIT+q+IjqEHa4zuyTlpMt5I1vKmt+w6DBEwaY
3Vgt2bebPlqoApuP3eXbkZxipcnTIGqcgLKQEREV+0PrPT45QdTJMe2g5K4J4AUKUKq8Fg3WiyuK
KggK+uwdE/jifjTqAtXqOo3vLWPoHo2DA11lABSKBlEOpp5jWOtsIAoinOFSsCS/cFucSNLbr2C6
m+li8wIDAQAB
-----END PUBLIC KEY-----`, // RSA公钥（需要从帆软系统获取）
  },
  production: {
    SERVER_URL: "https://your-fanruan-server.com", // 生产环境帆软地址
    PROJECT_NAME: "webroot", // 工程名
    USE_ENCRYPTION: true, // 使用加密方案
    ENABLE_TIMEOUT: true, // 生产环境建议启用超时
    PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlhUxCHnUXdUi/N6R3+GWUF171HtLNmCy
y2g1w/HVE0dabBgZJlj+NoFxeiPJBFOfZWReLRF/77HxT+1DJls81H/weHOWvgBidUmuhsWnAzOy
5rZ8IykV9rPeAjMU+mZYujD4P17yPeGfyU02hn/EIT+q+IjqEHa4zuyTlpMt5I1vKmt+w6DBEwaY
3Vgt2bebPlqoApuP3eXbkZxipcnTIGqcgLKQEREV+0PrPT45QdTJMe2g5K4J4AUKUKq8Fg3WiyuK
KggK+uwdE/jifjTqAtXqOo3vLWPoHo2DA11lABSKBlEOpp5jWOtsIAoinOFSsCS/cFucSNLbr2C6
m+li8wIDAQAB
-----END PUBLIC KEY-----`, // RSA公钥（需要从帆软系统获取）
  },
  staging: {
    SERVER_URL: "https://staging-fanruan.com", // 测试环境帆软地址
    PROJECT_NAME: "webroot", // 工程名
    USE_ENCRYPTION: true, // 使用加密方案
    ENABLE_TIMEOUT: false, // 测试环境可选
    PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlhUxCHnUXdUi/N6R3+GWUF171HtLNmCy
y2g1w/HVE0dabBgZJlj+NoFxeiPJBFOfZWReLRF/77HxT+1DJls81H/weHOWvgBidUmuhsWnAzOy
5rZ8IykV9rPeAjMU+mZYujD4P17yPeGfyU02hn/EIT+q+IjqEHa4zuyTlpMt5I1vKmt+w6DBEwaY
3Vgt2bebPlqoApuP3eXbkZxipcnTIGqcgLKQEREV+0PrPT45QdTJMe2g5K4J4AUKUKq8Fg3WiyuK
KggK+uwdE/jifjTqAtXqOo3vLWPoHo2DA11lABSKBlEOpp5jWOtsIAoinOFSsCS/cFucSNLbr2C6
m+li8wIDAQAB
-----END PUBLIC KEY-----`, // RSA公钥（需要从帆软系统获取）
  },
};

// 当前环境的帆软配置
export const CURRENT_FANRUAN_CONFIG = FANRUAN_CONFIG[CURRENT_ENV];

// 在开发环境下输出当前配置信息
EnvUtils.devOnly(() => {
  console.log("🚀 HuiLink App Environment Info:", {
    environment: CURRENT_ENV,
    baseUrl: API_CONFIG.BASE_URL,
    debug: CURRENT_CONFIG.DEBUG,
    logLevel: CURRENT_CONFIG.LOG_LEVEL,
  });

  console.log("📊 Fanruan Config Info:", {
    serverUrl: CURRENT_FANRUAN_CONFIG.SERVER_URL,
    projectName: CURRENT_FANRUAN_CONFIG.PROJECT_NAME,
    useEncryption: CURRENT_FANRUAN_CONFIG.USE_ENCRYPTION,
    enableTimeout: CURRENT_FANRUAN_CONFIG.ENABLE_TIMEOUT,
    hasPublicKey: CURRENT_FANRUAN_CONFIG.PUBLIC_KEY.includes("请在此处填入")
      ? "❌ 需要配置"
      : "✅ 已配置",
  });

  // 输出环境变量使用情况
  console.log("🔧 Environment Variables Status:", {
    baseUrlFromEnv: ENV_OVERRIDES.baseUrl
      ? "✅ Using env var"
      : "❌ Using default",
    debugFromEnv:
      ENV_OVERRIDES.debug !== undefined
        ? "✅ Using env var"
        : "❌ Using default",
    logLevelFromEnv: ENV_OVERRIDES.logLevel
      ? "✅ Using env var"
      : "❌ Using default",
    envVars: {
      EXPO_PUBLIC_API_BASE_URL:
        process.env.EXPO_PUBLIC_API_BASE_URL || "not set",
      EXPO_PUBLIC_DEBUG: process.env.EXPO_PUBLIC_DEBUG || "not set",
      EXPO_PUBLIC_LOG_LEVEL: process.env.EXPO_PUBLIC_LOG_LEVEL || "not set",
    },
  });
});
