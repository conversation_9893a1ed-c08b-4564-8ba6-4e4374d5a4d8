/**
 * 根布局组件
 * 配置应用的全局布局和导航
 */

import { AppInitializer } from "@/src/components/AppInitializer";
import { AuthGuard } from "@/src/components/AuthGuard";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import "@/global.css";
import { useTheme } from "@/src/stores/appStore";
import { Stack } from "expo-router";
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

function RootLayoutNav() {
  return (
    <AuthGuard>
      <SafeAreaProvider>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="login" options={{ headerShown: false, animation: 'none' }} />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="(dataplatform)" options={{ headerShown: false }} />
          <Stack.Screen name="(finance)" options={{ headerShown: false }} />
          <Stack.Screen name="theme-settings" options={{ presentation: 'modal', headerShown: true, title: '主题设置' }} />
          {/* 隐藏 index，因为 AuthGuard 会处理重定向 */}
          <Stack.Screen name="index" options={{ headerShown: false, "presentation": "modal" }} />
        </Stack>
      </SafeAreaProvider>
    </AuthGuard>
  );
}


export default function RootLayout() {
  // 使用useTheme hook确保主题变化的即时响应
  const { themeMode, isDarkMode } = useTheme();

  // 根据主题模式确定最终主题
  const finalTheme = React.useMemo(() => {
    if (themeMode === 'system') {
      return isDarkMode ? 'dark' : 'light';
    }
    return themeMode;
  }, [themeMode, isDarkMode]);

  return (
    <GluestackUIProvider mode={finalTheme}>
      <AppInitializer>
        <RootLayoutNav />
      </AppInitializer>
    </GluestackUIProvider>
  );
}
