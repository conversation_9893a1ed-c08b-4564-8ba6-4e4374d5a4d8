/**
 * 数据平台主页
 * 集成帆软系统的主要页面
 */

import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, BackHandler, Platform, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import DataPlatformWebView, { DataPlatformWebViewRef } from '../../src/components/DataPlatformWebView';
import { Button, Container, Text, ThemeStatusBar } from '../../src/components/ui';
import { useTheme } from '../../src/stores/appStore';

const DataPlatformScreen: React.FC = () => {
  const { theme } = useTheme();

  const [webViewCanGoBack, setWebViewCanGoBack] = useState(false);
  const webViewRef = useRef<DataPlatformWebViewRef>(null);

  // 处理返回
  const handleGoBack = () => {
    // 如果WebView可以后退，优先执行WebView后退
    if (webViewCanGoBack && webViewRef.current) {
      webViewRef.current.goBack();
      return;
    }

    // 否则返回到APP主页
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/');
    }
  };

  // 处理WebView错误
  const handleWebViewError = (error: string) => {
    // 在开发环境下显示详细错误信息
    if (__DEV__) {
      console.error('数据平台WebView错误:', error);
    }
  };

  // 处理加载开始
  const handleLoadStart = () => {
    // 可以在这里添加加载开始的逻辑
  };

  // 处理加载结束
  const handleLoadEnd = () => {
    // 可以在这里添加加载完成后的逻辑
  };

  // 处理WebView导航状态变化
  const handleWebViewNavigationStateChange = (canGoBack: boolean, _canGoForward: boolean) => {
    setWebViewCanGoBack(canGoBack);
  };

  // 返回键处理
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        handleGoBack();
        return true; // 阻止默认行为
      });

      return () => backHandler.remove();
    }
  }, [webViewCanGoBack]);

  // 显示帮助信息
  const showHelp = () => {
    const helpMessage = Platform.OS === 'ios'
      ? '这是集成的帆软数据平台，您可以在这里查看各种数据报表和分析。\n\n导航提示：\n• 使用顶部"后退"按钮在网页间导航\n• 使用底部工具栏进行页面控制\n• 点击"返回"退出数据平台\n\n如果遇到问题，请联系系统管理员。'
      : '这是集成的帆软数据平台，您可以在这里查看各种数据报表和分析。\n\n导航提示：\n• 使用返回键或顶部按钮在网页间导航\n• 使用底部工具栏进行页面控制\n\n如果遇到问题，请联系系统管理员。';

    Alert.alert(
      '数据平台帮助',
      helpMessage,
      [{ text: '确定' }]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background.secondary }]}>
      <ThemeStatusBar />
      
      {/* 顶部导航栏 */}
      <Container 
        style={[styles.header, { backgroundColor: theme.colors.background.primary }]}
        padding={4}
      >
        <Container style={styles.headerContent}>
          <Button
            title={webViewCanGoBack ? "← 后退" : "← 返回"}
            variant="text"
            size="small"
            onPress={handleGoBack}
            style={styles.backButton}
          />

          <Text variant="h6" weight="semibold" style={styles.title}>
            数据平台
          </Text>

          <Button
            title="帮助"
            variant="text"
            size="small"
            onPress={showHelp}
            style={styles.helpButton}
          />
        </Container>
      </Container>

      {/* WebView内容区域 */}
      <Container flex={1}>
        <DataPlatformWebView
          ref={webViewRef}
          onError={handleWebViewError}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onNavigationStateChange={handleWebViewNavigationStateChange}
        />
      </Container>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
    paddingVertical: 8,
  },
  backButton: {
    paddingHorizontal: 8,
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  helpButton: {
    paddingHorizontal: 8,
  },
});

export default DataPlatformScreen;
