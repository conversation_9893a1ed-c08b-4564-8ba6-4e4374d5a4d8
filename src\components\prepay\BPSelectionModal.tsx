/**
 * BP选择对话框组件
 */

import React, { useState } from 'react';
import {
  Modal,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text, Button } from '../ui';
import { BusinessPartner } from '../../types/prepay';

interface BPSelectionModalProps {
  visible: boolean;
  bpList: BusinessPartner[];
  selectedIndex: number;
  onClose: () => void;
  onConfirm: () => void;
  onCreateNew: () => void;
  onSelectionChange: (index: number) => void;
  theme: any;
}

const BPSelectionModal: React.FC<BPSelectionModalProps> = ({
  visible,
  bpList,
  selectedIndex,
  onClose,
  onConfirm,
  onCreateNew,
  onSelectionChange,
  theme,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
        {/* 头部 */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border.primary }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <Text variant="h6" weight="semibold" style={{ color: theme.colors.text.primary }}>
            请选择BP信息
          </Text>
          <View style={styles.placeholder} />
        </View>

        {/* BP列表 */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.bpList}>
            {bpList.map((bp, index) => (
              <TouchableOpacity
                key={`${bp.kunnr}-${index}`}
                style={[
                  styles.bpItem,
                  {
                    borderColor: selectedIndex === index 
                      ? theme.colors.primary 
                      : theme.colors.border.primary,
                    backgroundColor: selectedIndex === index 
                      ? theme.colors.primary + '10' 
                      : theme.colors.surface.primary,
                  }
                ]}
                onPress={() => onSelectionChange(index)}
                activeOpacity={0.7}
              >
                <View style={styles.radioContainer}>
                  <View style={[
                    styles.radioOuter,
                    { borderColor: selectedIndex === index ? theme.colors.primary : theme.colors.border.primary }
                  ]}>
                    {selectedIndex === index && (
                      <View style={[styles.radioInner, { backgroundColor: theme.colors.primary }]} />
                    )}
                  </View>
                </View>
                
                <View style={styles.bpInfo}>
                  <View style={styles.infoRow}>
                    <Text variant="body2" style={{ color: theme.colors.text.secondary }}>
                      客户编号:
                    </Text>
                    <Text variant="body2" weight="medium" style={{ color: theme.colors.text.primary }}>
                      {bp.kunnr}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text variant="body2" style={{ color: theme.colors.text.secondary }}>
                      姓名:
                    </Text>
                    <Text variant="body1" weight="medium" style={{ color: '#10B981' }}>
                      {bp.name1}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text variant="body2" style={{ color: theme.colors.text.secondary }}>
                      电话:
                    </Text>
                    <Text variant="body2" weight="medium" style={{ color: theme.colors.primary }}>
                      {bp.telf1}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* 创建新BP按钮 */}
          <View style={styles.createButtonContainer}>
            <TouchableOpacity
              style={[styles.createButton, { backgroundColor: theme.colors.primary }]}
              onPress={onCreateNew}
              activeOpacity={0.8}
            >
              <Text style={styles.createButtonText}>
                未找到BP信息，创建BP
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* 底部按钮 */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border.primary }]}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: theme.colors.border.primary }]}
              onPress={onClose}
              activeOpacity={0.7}
            >
              <Text style={[styles.cancelButtonText, { color: theme.colors.text.primary }]}>
                关闭
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.confirmButton,
                {
                  backgroundColor: selectedIndex >= 0 ? theme.colors.primary : theme.colors.text.tertiary,
                }
              ]}
              onPress={onConfirm}
              disabled={selectedIndex < 0}
              activeOpacity={0.8}
            >
              <Text style={styles.confirmButtonText}>确认</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  bpList: {
    gap: 12,
  },
  bpItem: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    minHeight: 80,
  },
  radioContainer: {
    marginRight: 12,
    justifyContent: 'center',
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  bpInfo: {
    flex: 1,
    gap: 4,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  createButtonContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  createButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: '60%',
    alignItems: 'center',
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BPSelectionModal;
