/**
 * 系统主题Hook
 * 基于React Native的useColorScheme和Expo最佳实践
 * 简化的系统主题监听实现
 */

import { useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { ThemeMode } from '../types';

/**
 * 系统主题Hook
 * 提供简化的系统主题监听功能
 */
export const useSystemTheme = () => {
  const systemColorScheme = useColorScheme();
  const [isListening, setIsListening] = useState(false);

  /**
   * 获取当前系统主题
   */
  const getSystemTheme = (): 'light' | 'dark' => {
    return systemColorScheme === 'dark' ? 'dark' : 'light';
  };

  /**
   * 根据主题模式获取实际主题
   */
  const getActualTheme = (themeMode: ThemeMode): 'light' | 'dark' => {
    if (themeMode === 'system') {
      return getSystemTheme();
    }
    return themeMode;
  };

  /**
   * 检查是否应该使用暗色主题
   */
  const shouldUseDarkTheme = (themeMode: ThemeMode): boolean => {
    return getActualTheme(themeMode) === 'dark';
  };

  return {
    systemColorScheme,
    getSystemTheme,
    getActualTheme,
    shouldUseDarkTheme,
    isListening,
    setIsListening,
  };
};

/**
 * 主题变化监听Hook
 * 监听系统主题变化并执行回调
 */
export const useThemeChangeListener = (
  themeMode: ThemeMode,
  onThemeChange: (isDark: boolean) => void
) => {
  const { systemColorScheme, shouldUseDarkTheme } = useSystemTheme();

  useEffect(() => {
    // 只在"跟随系统"模式下监听系统主题变化
    if (themeMode === 'system') {
      const isDark = shouldUseDarkTheme(themeMode);
      onThemeChange(isDark);
    }
  }, [systemColorScheme, themeMode, shouldUseDarkTheme, onThemeChange]);

  return {
    isSystemMode: themeMode === 'system',
    currentSystemTheme: systemColorScheme,
  };
};

export default useSystemTheme;
