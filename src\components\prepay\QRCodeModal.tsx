/**
 * 二维码显示对话框组件
 */

import React from 'react';
import {
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import QRCode from 'react-native-qrcode-svg';
import { Text } from '../ui';
import { QRCodeInfo } from '../../types/prepay';

interface QRCodeModalProps {
  visible: boolean;
  qrCodeData: string;
  qrCodeInfo: QRCodeInfo;
  isLoading: boolean;
  onClose: () => void;
  theme: any;
}

const QRCodeModal: React.FC<QRCodeModalProps> = ({
  visible,
  qrCodeData,
  qrCodeInfo,
  isLoading,
  onClose,
  theme,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
        {/* 头部 */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border.primary }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <Text variant="h6" weight="semibold" style={{ color: theme.colors.text.primary }}>
            收款二维码
          </Text>
          <View style={styles.placeholder} />
        </View>

        {/* 内容区域 */}
        <View style={styles.content}>
          {isLoading ? (
            // 加载状态
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.loadingText, { color: theme.colors.text.secondary }]}>
                正在生成二维码...
              </Text>
            </View>
          ) : (
            // 二维码内容
            <View style={styles.qrCodeContainer}>
              {/* 二维码 */}
              <View style={[styles.qrCodeWrapper, { backgroundColor: '#FFFFFF' }]}>
                {qrCodeData ? (
                  <QRCode
                    value={qrCodeData}
                    size={260}
                    color="#000000"
                    backgroundColor="#FFFFFF"
                  />
                ) : (
                  <View style={styles.qrCodePlaceholder}>
                    <Ionicons name="qr-code-outline" size={100} color={theme.colors.text.tertiary} />
                    <Text style={{ color: theme.colors.text.tertiary, marginTop: 8 }}>
                      二维码生成失败
                    </Text>
                  </View>
                )}
              </View>

              {/* 扫码提示 */}
              <View style={styles.scanTip}>
                <Ionicons name="scan-outline" size={20} color={theme.colors.text.secondary} />
                <Text style={[styles.scanText, { color: theme.colors.text.secondary }]}>
                  请使用支付宝或微信扫码支付
                </Text>
              </View>

              {/* 订单信息 */}
              <View style={[styles.orderInfo, { backgroundColor: theme.colors.surface.secondary }]}>
                <Text variant="h6" weight="semibold" style={[styles.orderTitle, { color: theme.colors.text.primary }]}>
                  订单信息
                </Text>
                
                <View style={styles.infoGrid}>
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: theme.colors.text.secondary }]}>
                      门店代码
                    </Text>
                    <Text style={[styles.infoValue, { color: theme.colors.text.primary }]}>
                      {qrCodeInfo.code}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: theme.colors.text.secondary }]}>
                      客户名称
                    </Text>
                    <Text style={[styles.infoValue, { color: theme.colors.text.primary }]}>
                      {qrCodeInfo.name}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: theme.colors.text.secondary }]}>
                      服务顾问
                    </Text>
                    <Text style={[styles.infoValue, { color: theme.colors.text.primary }]}>
                      {qrCodeInfo.consultant}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: theme.colors.text.secondary }]}>
                      订单金额
                    </Text>
                    <Text style={[styles.infoValue, styles.amountText, { color: '#EF4444' }]}>
                      ¥{qrCodeInfo.money}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}
        </View>

        {/* 底部按钮 */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border.primary }]}>
          <TouchableOpacity
            style={[styles.closeFooterButton, { backgroundColor: theme.colors.surface.secondary, borderColor: theme.colors.border.primary }]}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Text style={[styles.closeFooterButtonText, { color: theme.colors.text.primary }]}>
              关闭
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  qrCodeContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 20,
  },
  qrCodeWrapper: {
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  qrCodePlaceholder: {
    width: 260,
    height: 260,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanTip: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
    gap: 8,
  },
  scanText: {
    fontSize: 14,
  },
  orderInfo: {
    width: '100%',
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
  },
  orderTitle: {
    marginBottom: 16,
    textAlign: 'center',
  },
  infoGrid: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '700',
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  closeFooterButton: {
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  closeFooterButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default QRCodeModal;
