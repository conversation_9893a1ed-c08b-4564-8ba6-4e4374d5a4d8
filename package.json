{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@types/node-forge": "^1.3.13", "axios": "^1.10.0", "babel-plugin-module-resolver": "^5.0.2", "crypto-js": "^4.2.0", "expo": "^53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "node-forge": "^1.3.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/crypto-js": "^4.2.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jscodeshift": "^0.15.2", "typescript": "~5.8.3"}, "private": true}