/**
 * 我的 Tab 页面
 * 用户个人信息和设置
 */

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useRouter } from 'expo-router';
import React from 'react';
import { ActionSheetIOS, Alert, Dimensions, Platform, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { Button, Card, Container, Text } from '../../src/components/ui';
import { useAuthStore } from '../../src/stores';
import { useTheme } from '../../src/stores/appStore';

const ProfileScreen: React.FC = () => {
  const { theme, themeMode, setTheme } = useTheme();
  const { user, logout } = useAuthStore();
  const router = useRouter();
  const insets = useSafeAreaInsets();



  // 计算精确的可用高度
  const screenHeight = Dimensions.get('window').height;
  const tabBarHeight = 85; // 从_layout.tsx获取的TAB栏高度
  const availableHeight = screenHeight - tabBarHeight - insets.top;



  // 主题选项配置
  const themeOptions = [
    { value: 'light', label: '浅色', icon: 'sun-o' },
    { value: 'dark', label: '深色', icon: 'moon-o' },
    { value: 'system', label: '跟随系统', icon: 'mobile' },
  ];

  // 获取当前主题显示信息
  const getCurrentThemeInfo = () => {
    const option = themeOptions.find(opt => opt.value === themeMode);
    return option || themeOptions[0];
  };

  // 显示主题选择ActionSheet
  const showThemeActionSheet = () => {
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['取消', '浅色主题', '深色主题', '跟随系统'],
          cancelButtonIndex: 0,
          title: '选择主题',
        },
        (buttonIndex) => {
          if (buttonIndex === 1) {
            setTheme('light');
          } else if (buttonIndex === 2) {
            setTheme('dark');
          } else if (buttonIndex === 3) {
            setTheme('system');
          }
        }
      );
    } else {
      // Android使用Alert
      Alert.alert(
        '选择主题',
        '',
        [
          { text: '取消', style: 'cancel' },
          { text: '浅色主题', onPress: () => setTheme('light') },
          { text: '深色主题', onPress: () => setTheme('dark') },
          { text: '跟随系统', onPress: () => setTheme('system') },
        ]
      );
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      '确认退出',
      '您确定要退出登录吗？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            await logout();
          },
        },
      ]
    );
  };

  // 设置选项类型定义
  interface SettingOption {
    id: number;
    title: string;
    icon: string;
    subtitle?: string;
    onPress?: () => void;
    isNavigable?: boolean; // 新增一个标志，用于判断是否是导航项
  }

  // 设置选项
  const settingOptions: SettingOption[] = [
    {
      id: 1,
      title: '账户设置',
      icon: 'cog',
      onPress: () => Alert.alert('提示', '账户设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 2,
      title: '主题设置',
      icon: getCurrentThemeInfo().icon,
      subtitle: getCurrentThemeInfo().label,
      onPress: showThemeActionSheet,
      isNavigable: false,
    },
    {
      id: 3,
      title: '通知设置',
      icon: 'bell',
      onPress: () => Alert.alert('提示', '通知设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 4,
      title: '隐私设置',
      icon: 'shield',
      onPress: () => Alert.alert('提示', '隐私设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 5,
      title: '帮助与反馈',
      icon: 'question-circle',
      onPress: () => Alert.alert('提示', '帮助与反馈功能开发中'),
      isNavigable: true,
    },
    {
      id: 6,
      title: '关于应用',
      icon: 'info-circle',
      onPress: () => Alert.alert('关于', 'HuiLink移动应用 v1.0.0'),
      isNavigable: true,
    },

  ];

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background.secondary }]}
      edges={['top']}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          { minHeight: availableHeight }
        ]}
        showsVerticalScrollIndicator={false}
      >
        <Container padding={6} style={{ flex: 1, justifyContent: 'space-between' }}>
          {/* 用户信息卡片 */}
          <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[3] }}>
            <Container>
              <View style={styles.userHeader}>
                <View style={styles.avatarContainer}>
                  <FontAwesome name="user-circle" size={60} color={theme.colors.primary} />
                </View>
                <View style={styles.userInfo}>
                  <Text variant="h4" weight="semibold" style={{ marginBottom: theme.spacing[1] }}>
                    {user?.realName || user?.account || '用户'}
                  </Text>
                  <Text variant="body1" color="secondary">
                    {user?.account}
                  </Text>
                  {user?.orgName && (
                    <Text variant="caption" color="secondary" style={{ marginTop: theme.spacing[1] }}>
                      {user.orgName}
                    </Text>
                  )}
                </View>
              </View>
            </Container>
          </Card>

          {/* 详细信息 */}
          <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[3] }}>
            <Container>
              <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[3] }}>
                个人信息
              </Text>

              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  账号：
                </Text>
                <Text variant="body1">{user?.account}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  姓名：
                </Text>
                <Text variant="body1">{user?.realName || '未设置'}</Text>
              </View>

              {user?.orgName && (
                <View style={styles.infoRow}>
                  <Text variant="body1" color="secondary" style={styles.label}>
                    组织：
                  </Text>
                  <Text variant="body1">{user.orgName}</Text>
                </View>
              )}


            </Container>
          </Card>

          {/* 设置选项 */}
          <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[6] }}>
            <Container>
              <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[3] }}>
                设置
              </Text>

              {settingOptions.map((option) => (
                <View key={option.id}>
                  <TouchableOpacity
                    style={[
                      styles.settingItem,
                      { backgroundColor: theme.colors.surface.primary }
                    ]}
                    onPress={option.onPress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.settingLeft}>
                      <View style={[
                        styles.settingIconContainer,
                        { backgroundColor: theme.colors.primary + '15' }
                      ]}>
                        <FontAwesome
                          name={option.icon as any}
                          size={18}
                          color={theme.colors.primary}
                        />
                      </View>
                      <View style={styles.settingTextContainer}>
                        <Text variant="body1">{option.title}</Text>
                      </View>
                    </View>
                    <View style={styles.settingRight}>
                      {option.subtitle && (
                        <Text variant="body2" color="secondary" style={{ marginRight: 8 }}>
                          {option.subtitle}
                        </Text>
                      )}
                      {option.isNavigable && (
                        <FontAwesome
                          name="chevron-right"
                          size={14}
                          color={theme.colors.text.tertiary}
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                </View>
              ))}
            </Container>
            {/* 退出登录 */}
            <View style={[styles.logoutContainer, { paddingBottom: Math.max(insets.bottom, 20) }]}>
              <Button
                title="退出登录"
                variant="outline"
                onPress={handleLogout}
                style={styles.logoutButton}
              />
            </View>
          </Card>
        </Container>
      </ScrollView>


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    minWidth: 60,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 12,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingTextContainer: {
    // Removed flex: 1 to allow the right side to align properly
  },
  logoutContainer: {
    marginTop: 24,
    paddingHorizontal: 4,
  },
  logoutButton: {
    marginBottom: 6,
  },
  // Modal样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    borderRadius: 16,
    margin: 20,
    minWidth: 280,
    maxWidth: '85%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  themeOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  themeOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ProfileScreen;