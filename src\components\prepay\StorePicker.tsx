/**
 * 门店选择器组件
 */

import React, { useState } from 'react';
import {
  Modal,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../ui';
import { StoreInfo } from '../../types/prepay';
import { PrepayService } from '../../services/prepayService';

interface StorePickerProps {
  stores: StoreInfo[];
  selectedStore: StoreInfo | null;
  onStoreSelect: (store: StoreInfo) => void;
  placeholder?: string;
  error?: string;
  theme: any;
}

const StorePicker: React.FC<StorePickerProps> = ({
  stores,
  selectedStore,
  onStoreSelect,
  placeholder = "请选择门店",
  error,
  theme,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 过滤门店列表
  const filteredStores = stores.filter(store =>
    PrepayService.getStoreLabel(store).toLowerCase().includes(searchText.toLowerCase())
  );

  const handleStoreSelect = (store: StoreInfo) => {
    onStoreSelect(store);
    setModalVisible(false);
    setSearchText('');
  };

  const getDisplayText = () => {
    if (selectedStore) {
      return PrepayService.getStoreLabel(selectedStore);
    }
    return placeholder;
  };

  return (
    <>
      {/* 选择器触发按钮 */}
      <TouchableOpacity
        style={[
          styles.pickerButton,
          {
            borderColor: error ? '#EF4444' : theme.colors.border.primary,
            backgroundColor: theme.colors.surface.primary,
          }
        ]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.pickerText,
            {
              color: selectedStore 
                ? theme.colors.text.primary 
                : theme.colors.text.tertiary
            }
          ]}
          numberOfLines={1}
        >
          {getDisplayText()}
        </Text>
        <Ionicons
          name="chevron-down"
          size={20}
          color={theme.colors.text.secondary}
        />
      </TouchableOpacity>

      {/* 错误提示 */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* 选择Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background.primary }]}>
          {/* 头部 */}
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border.primary }]}>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={theme.colors.text.primary} />
            </TouchableOpacity>
            <Text variant="h6" weight="semibold" style={{ color: theme.colors.text.primary }}>
              选择门店
            </Text>
            <View style={styles.placeholder} />
          </View>

          {/* 搜索框 */}
          <View style={styles.searchContainer}>
            <View style={[styles.searchBox, { 
              backgroundColor: theme.colors.surface.secondary,
              borderColor: theme.colors.border.primary 
            }]}>
              <Ionicons name="search" size={20} color={theme.colors.text.tertiary} />
              <TextInput
                style={[styles.searchInput, { color: theme.colors.text.primary }]}
                placeholder="搜索门店..."
                placeholderTextColor={theme.colors.text.tertiary}
                value={searchText}
                onChangeText={setSearchText}
              />
            </View>
          </View>

          {/* 门店列表 */}
          <ScrollView style={styles.storeList} showsVerticalScrollIndicator={false}>
            {filteredStores.map((store, index) => (
              <TouchableOpacity
                key={`${store.compCode}-${store.profitCenter}`}
                style={[
                  styles.storeItem,
                  {
                    backgroundColor: selectedStore?.compCode === store.compCode && 
                                   selectedStore?.profitCenter === store.profitCenter
                      ? theme.colors.primary + '20'
                      : 'transparent',
                    borderBottomColor: theme.colors.border.primary,
                  }
                ]}
                onPress={() => handleStoreSelect(store)}
                activeOpacity={0.6}
              >
                <View style={styles.storeInfo}>
                  <Text
                    variant="body1"
                    weight="medium"
                    style={{ color: theme.colors.text.primary }}
                  >
                    {PrepayService.getStoreLabel(store)}
                  </Text>
                  <Text
                    variant="body2"
                    style={{ color: theme.colors.text.secondary, marginTop: 2 }}
                  >
                    代码: {store.compCode} | 利润中心: {store.profitCenter}
                  </Text>
                </View>
                {selectedStore?.compCode === store.compCode && 
                 selectedStore?.profitCenter === store.profitCenter && (
                  <Ionicons
                    name="checkmark-circle"
                    size={24}
                    color={theme.colors.primary}
                  />
                )}
              </TouchableOpacity>
            ))}
            
            {filteredStores.length === 0 && (
              <View style={styles.emptyContainer}>
                <Text style={{ color: theme.colors.text.tertiary }}>
                  {searchText ? '未找到匹配的门店' : '暂无门店数据'}
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 2,
    minHeight: 48,
  },
  pickerText: {
    flex: 1,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 4,
  },
  placeholder: {
    width: 32,
  },
  searchContainer: {
    padding: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    paddingVertical: 4,
  },
  storeList: {
    flex: 1,
  },
  storeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  storeInfo: {
    flex: 1,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
});

export default StorePicker;
