/**
 * 预收款相关类型定义
 */

// 门店信息
export interface StoreInfo {
  compCode: string;        // 公司代码
  compName: string;        // 公司名称
  profitCenter: string;    // 利润中心
}

// 业务伙伴信息
export interface BusinessPartner {
  kunnr: string;          // 客户编号
  name1: string;          // 客户名称
  telf1: string;          // 联系电话
  title: string;          // 称谓
  bukrs: string;          // 公司代码
}

// 表单数据
export interface PrepayFormData {
  customerName: string;    // 客户名称 (name1)
  title: string;          // 称谓 (0001: 女士, 0002: 先生, 0003: 公司)
  phone: string;          // 联系电话 (telf1)
  store: string;          // 门店选择 (bukrss)
  profitCenter: string;   // 利润中心
  amount: string;         // 预收定金金额 (money)
}

// 预付款请求参数
export interface PrepayRequest {
  bukrs: string;          // 公司代码
  newko: string;          // 客户编号
  wrbtr: string;          // 金额
  zfbdt: string;          // 年月日
  xref1: string;          // 年月日
  budat: string;          // 年月日
  prctr: string;          // 利润中心
  BpName: string;         // 客户名称
}

// BP创建请求参数
export interface CreateBPRequest {
  name1: string;          // 客户名称
  title: string;          // 称谓
  telf1: string;          // 联系电话
  bukrs: string;          // 门店代码
  bukrss: string;         // 门店选择值
  money: string;          // 金额
  kunnr: string;          // 客户编号
  profit: string;         // 利润中心
}

// BP查询请求参数
export interface GetBPRequest {
  name1: string;          // 客户名称
  title: string;          // 称谓
  telf1: string;          // 联系电话
  bukrs: string;          // 门店代码
  bukrss: string;         // 门店选择值
  money: string;          // 金额
  kunnr: string;          // 客户编号
  profit: string;         // 利润中心
}

// 二维码信息
export interface QRCodeInfo {
  consultant: string;     // 顾问
  name: string;          // 客户名称
  money: string;         // 金额
  code: string;          // 门店代码/名称
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
}

// 预付款创建响应
export interface PrepayResponse {
  id: string;
  msg: string;
}

// BP创建响应
export interface CreateBPResponse {
  kunnr: string;
  name1: string;
  msg: string;
}

// 关键字验证请求
export interface KeywordValidateRequest {
  requiredKeyword: string;
}

// BP关键字日志请求
export interface BpKeywordLogRequest {
  bpName: string;
}

// 表单验证错误
export interface FormErrors {
  customerName?: string;
  phone?: string;
  store?: string;
  amount?: string;
}

// 组件状态
export interface PrepayState {
  // 表单数据
  formData: PrepayFormData;
  formErrors: FormErrors;
  
  // 数据列表
  storeList: StoreInfo[];
  bpList: BusinessPartner[];
  
  // UI状态
  isLoading: boolean;
  isSubmitting: boolean;
  showBPSelection: boolean;
  showCreateBPConfirm: boolean;
  showQRCode: boolean;
  
  // 选择状态
  selectedBPIndex: number;
  
  // 二维码相关
  qrCodeData: string;
  qrCodeInfo: QRCodeInfo;
  
  // 当前选择的门店信息
  selectedStore: StoreInfo | null;
}

// 利润中心选项
export interface ProfitCenterOption {
  label: string;
  value: string;
}

// 称谓选项
export interface TitleOption {
  label: string;
  value: string;
}

// 预收款报表查询参数
export interface PrepayReportRequest {
  startDate: string; // 查询日期 YYYY-MM-DD
}

// 预收款报表数据项
export interface PrepayReportItem {
  newko: string;           // 客户编号
  bpName: string;          // 客户名称
  wrbtr: number;           // 支付金额
  payTime?: string;        // 支付时间
  payType?: string;        // 支付类型
  companyCode?: string;    // 门店编码
  compName?: string;       // 门店名称
  belnr?: string;          // 凭证编号
  merOrderNumber?: string; // 商户支付订单号
}
