/**
 * 创建BP确认对话框组件
 */

import React from 'react';
import {
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../ui';

interface CreateBPModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  theme: any;
}

const CreateBPModal: React.FC<CreateBPModalProps> = ({
  visible,
  onClose,
  onConfirm,
  theme,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
          {/* 头部 */}
          <View style={[styles.header, { borderBottomColor: theme.colors.border.primary }]}>
            <View style={styles.headerContent}>
              <Ionicons name="person-add-outline" size={20} color={theme.colors.primary} />
              <Text variant="h6" weight="semibold" style={{ color: theme.colors.text.primary }}>
                扩展客户信息
              </Text>
            </View>
          </View>

          {/* 内容 */}
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <View style={[styles.iconWrapper, { backgroundColor: theme.colors.primary + '20' }]}>
                <Ionicons name="help-circle-outline" size={48} color={theme.colors.primary} />
              </View>
            </View>
            
            <Text
              variant="body1"
              style={[styles.message, { color: theme.colors.text.primary }]}
            >
              需要将客户信息扩展到当前门店，是否继续？
            </Text>

            <Text
              variant="body2"
              style={[styles.subMessage, { color: theme.colors.text.secondary }]}
            >
              将为该客户在当前门店创建业务伙伴记录
            </Text>
          </View>

          {/* 底部按钮 */}
          <View style={styles.footer}>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: theme.colors.border.primary }]}
                onPress={onClose}
                activeOpacity={0.7}
              >
                <Text style={[styles.cancelButtonText, { color: theme.colors.text.primary }]}>
                  取消
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.confirmButton, { backgroundColor: theme.colors.primary }]}
                onPress={onConfirm}
                activeOpacity={0.8}
              >
                <Text style={styles.confirmButtonText}>确定</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  message: {
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 24,
  },
  subMessage: {
    textAlign: 'center',
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreateBPModal;
