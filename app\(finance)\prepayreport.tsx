/**
 * 收款报表页面
 * 显示预收款的统计报表和明细信息
 */

import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router, Stack } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, FlatList, Modal, Platform, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, Card, Container, Text } from '../../src/components/ui';
import { useTheme } from '../../src/stores/appStore';
import { PrepayService } from '../../src/services/prepayService';
import { PrepayReportItem } from '../../src/types/prepay';

// 优化的列表项组件
interface PrepayReportListItemProps {
  item: PrepayReportItem;
  onShowDetail: (item: PrepayReportItem) => void;
  formatAmount: (amount: number) => string;
}

const PrepayReportListItem = React.memo<PrepayReportListItemProps>(({
  item,
  onShowDetail,
  formatAmount
}) => {
  const { theme } = useTheme();

  const handleDetailPress = React.useCallback(() => {
    onShowDetail(item);
  }, [item, onShowDetail]);

  return (
    <Card padding={4} elevation={1} style={styles.listItem}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Text variant="body1" weight="semibold" numberOfLines={1}>
            {item.bpName}
          </Text>
          <Text variant="body2" color="secondary" style={styles.customerCode}>
            客户编号: {item.newko}
          </Text>
        </View>
        <View style={styles.itemAmount}>
          <Text variant="h6" weight="semibold" color="primary">
            {formatAmount(item.wrbtr)}
          </Text>
        </View>
      </View>

      <View style={styles.itemFooter}>
        {item.payTime && (
          <Text variant="body2" color="secondary">
            支付时间: {item.payTime}
          </Text>
        )}
        <TouchableOpacity
          style={styles.detailButton}
          onPress={handleDetailPress}
        >
          <Ionicons name="information-circle-outline" size={16} color={theme.colors.primary} />
          <Text variant="body2" color="primary" style={styles.detailText}>
            查看明细
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
});

PrepayReportListItem.displayName = 'PrepayReportListItem';

const PrepayReportScreen: React.FC = () => {
  const { theme } = useTheme();
  
  // 查询参数
  const [queryParams, setQueryParams] = useState({
    startDate: new Date().toISOString().split('T')[0], // 默认今天
  });

  // 报表数据
  const [tableData, setTableData] = useState<PrepayReportItem[]>([]);
  const [loading, setLoading] = useState(false);

  // 明细弹窗
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<PrepayReportItem | null>(null);

  // 日期选择器状态
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());

  // 页面加载时获取数据
  useEffect(() => {
    handleQuery();
  }, []);

  // 查询数据
  const handleQuery = async () => {
    if (!queryParams.startDate) {
      Alert.alert('提示', '请选择查询日期');
      return;
    }

    setLoading(true);

    try {
      // 调用真实API
      const reportData = await PrepayService.getPrepayReport({
        startDate: queryParams.startDate
      });

      setTableData(reportData);
    } catch (error: any) {
      console.error('查询预收款报表失败:', error);
      Alert.alert('查询失败', error.message || '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 格式化金额 - 使用useCallback优化性能
  const formatAmount = React.useCallback((amount: number): string => {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
  }, []);

  // 显示明细
  const showDetail = (item: PrepayReportItem) => {
    setSelectedItem(item);
    setDetailVisible(true);
  };

  // 处理日期选择 - iOS端只更新临时日期，不关闭选择器
  const handleDateChange = (_event: any, selectedDate?: Date) => {
    if (Platform.OS === 'ios') {
      // iOS端：只更新临时日期，不关闭选择器
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    } else {
      // Android端：保持原有逻辑
      setShowDatePicker(false);
      if (selectedDate) {
        const formattedDate = selectedDate.toISOString().split('T')[0];
        setQueryParams(prev => ({ ...prev, startDate: formattedDate }));
      }
    }
  };

  // 确认日期选择 - iOS端专用
  const confirmDateSelection = () => {
    const formattedDate = tempDate.toISOString().split('T')[0];
    setQueryParams(prev => ({ ...prev, startDate: formattedDate }));
    setShowDatePicker(false);
  };

  // 取消日期选择 - iOS端专用
  const cancelDateSelection = () => {
    setShowDatePicker(false);
    // 重置临时日期为当前选中的日期
    setTempDate(new Date(queryParams.startDate || new Date().toISOString().split('T')[0]));
  };

  // 显示日期选择器
  const showDatePickerModal = () => {
    // 初始化临时日期为当前选中的日期
    setTempDate(new Date(queryParams.startDate || new Date().toISOString().split('T')[0]));
    setShowDatePicker(true);
  };

  // 渲染列表项 - 使用React.memo优化性能
  const renderItem = React.useCallback(({ item }: { item: PrepayReportItem }) => (
    <PrepayReportListItem
      item={item}
      onShowDetail={showDetail}
      formatAmount={formatAmount}
    />
  ), [formatAmount]);

  // 优化的keyExtractor
  const keyExtractor = React.useCallback((item: PrepayReportItem, index: number) =>
    `${item.newko}-${item.wrbtr}-${index}`, []
  );

  // 优化的空状态组件 - 跨平台适配
  const emptyComponent = React.useMemo(() => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          {Platform.OS === 'ios' ? (
            // iOS 简洁风格
            <View style={styles.loadingContentIOS}>
              <ActivityIndicator
                size="large"
                color={theme.colors.primary}
                style={styles.loadingSpinner}
              />
              <Text variant="body1" color="secondary" style={styles.loadingTextIOS}>
                正在加载...
              </Text>
            </View>
          ) : (
            // Android Material Design 风格
            <View style={styles.loadingContentAndroid}>
              <ActivityIndicator
                size="large"
                color={theme.colors.primary}
                style={styles.loadingSpinner}
              />
              <Text variant="body1" color="secondary" style={styles.loadingTextAndroid}>
                正在加载数据...
              </Text>
            </View>
          )}
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="document-outline" size={48} color={theme.colors.text.secondary} />
        <Text variant="body1" color="secondary" style={styles.emptyText}>
          暂无数据
        </Text>
        <Text variant="body2" color="secondary" style={styles.emptyHint}>
          请选择日期后点击查询按钮
        </Text>
      </View>
    );
  }, [loading, theme.colors.primary, theme.colors.text.secondary]);

  // 移除getItemLayout以避免滚动对齐问题
  // 注意：移除getItemLayout可能会轻微影响性能，但能解决滚动时的"向下弹"问题

  // 计算总金额
  const totalAmount = tableData.reduce((sum, item) => sum + item.wrbtr, 0);

  // 返回按钮
  const renderBackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={styles.backButton}
    >
      <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: '收款报表',
          headerShown: true,
          headerLeft: renderBackButton,
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTitleStyle: {
            color: theme.colors.text.primary,
          },
        }}
      />
      
      <View style={[styles.container, { backgroundColor: theme.colors.background.secondary }]}>
        <Container flex={1} padding={4}>
          {/* 查询条件卡片 */}
          <Card padding={6} elevation={2} style={styles.queryCard}>
            <View style={styles.queryRow}>
              <View style={styles.dateInput}>
                <Text variant="body2" style={styles.label}>查询日期</Text>
                <TouchableOpacity
                  style={styles.dateInputContainer}
                  onPress={showDatePickerModal}
                >
                  <Text style={styles.dateInputText}>
                    {queryParams.startDate || 'YYYY-MM-DD'}
                  </Text>
                  <Ionicons name="calendar-outline" size={20} color={theme.colors.text.secondary} />
                </TouchableOpacity>
              </View>

              <Button
                title="查询"
                onPress={handleQuery}
                loading={loading}
                style={styles.queryButton}
              />
            </View>
          </Card>

          {/* 统计信息卡片 */}
          {tableData.length > 0 && (
            <Card padding={6} elevation={2} style={styles.summaryCard}>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <Text variant="body2" color="secondary">总笔数</Text>
                  <Text variant="h5" weight="semibold">{tableData.length}</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text variant="body2" color="secondary">总金额</Text>
                  <Text variant="h5" weight="semibold" color="primary">
                    {formatAmount(totalAmount)}
                  </Text>
                </View>
              </View>
            </Card>
          )}

          {/* 报表列表 */}
          <Card padding={0} elevation={2} style={styles.listCard}>
            <View style={styles.listHeader}>
              <Text variant="h6" weight="semibold">收款明细</Text>
            </View>
            
            <FlatList
              data={tableData}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={emptyComponent}
              contentContainerStyle={styles.listContent}
              // 下拉刷新配置 - 只在有数据时启用，避免双重加载指示器
              refreshing={tableData.length > 0 && loading}
              onRefresh={handleQuery}
              refreshControl={
                <RefreshControl
                  refreshing={tableData.length > 0 && loading}
                  onRefresh={handleQuery}
                  tintColor={theme.colors.primary}
                  colors={[theme.colors.primary]}
                  progressBackgroundColor={theme.colors.background.primary}
                />
              }
              // 滚动行为优化
              bounces={true}
              alwaysBounceVertical={false}
              overScrollMode="auto"
              scrollEventThrottle={16}
              // 性能优化配置
              removeClippedSubviews={true}
              maxToRenderPerBatch={8}
              updateCellsBatchingPeriod={100}
              initialNumToRender={8}
              windowSize={8}
              disableVirtualization={false}
              legacyImplementation={false}
            />
          </Card>
        </Container>

        {/* 明细弹窗 - 跨平台适配 */}
        <Modal
          visible={detailVisible}
          transparent
          animationType={Platform.OS === 'ios' ? 'slide' : 'fade'}
          onRequestClose={() => setDetailVisible(false)}
        >
          {Platform.OS === 'ios' ? (
            // iOS Bottom Sheet 样式
            <View style={styles.bottomSheetOverlay}>
              <TouchableOpacity
                style={styles.bottomSheetBackdrop}
                activeOpacity={1}
                onPress={() => setDetailVisible(false)}
              />
              <View style={styles.bottomSheetContent}>
                {/* iOS 样式的拖拽指示器 */}
                <View style={styles.bottomSheetHandle} />

                <View style={styles.bottomSheetHeader}>
                  <Text variant="h6" weight="semibold">收款明细</Text>
                </View>

                {selectedItem && (
                  <View style={styles.bottomSheetBody}>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">客户编号</Text>
                      <Text variant="body1">{selectedItem.newko}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">客户名称</Text>
                      <Text variant="body1">{selectedItem.bpName}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">支付金额</Text>
                      <Text variant="h6" weight="semibold" color="primary">
                        {formatAmount(selectedItem.wrbtr)}
                      </Text>
                    </View>
                    {selectedItem.payTime && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">支付时间</Text>
                        <Text variant="body1">{selectedItem.payTime}</Text>
                      </View>
                    )}
                    {selectedItem.payType && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">支付类型</Text>
                        <Text variant="body1">{selectedItem.payType}</Text>
                      </View>
                    )}
                    {selectedItem.belnr && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">凭证编号</Text>
                        <Text variant="body1">{selectedItem.belnr}</Text>
                      </View>
                    )}
                    {selectedItem.companyCode && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">门店编码</Text>
                        <Text variant="body1">{selectedItem.companyCode}</Text>
                      </View>
                    )}
                    {selectedItem.compName && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">门店名称</Text>
                        <Text variant="body1">{selectedItem.compName}</Text>
                      </View>
                    )}
                    {selectedItem.merOrderNumber && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">商户支付订单号</Text>
                        <Text variant="body1">{selectedItem.merOrderNumber}</Text>
                      </View>
                    )}
                  </View>
                )}
              </View>
            </View>
          ) : (
            // Android Modal 样式
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text variant="h6" weight="semibold">收款明细</Text>
                  <TouchableOpacity
                    onPress={() => setDetailVisible(false)}
                    style={styles.closeButton}
                  >
                    <Ionicons name="close" size={24} color={theme.colors.text.primary} />
                  </TouchableOpacity>
                </View>

                {selectedItem && (
                  <View style={styles.modalBody}>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">客户编号</Text>
                      <Text variant="body1">{selectedItem.newko}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">客户名称</Text>
                      <Text variant="body1">{selectedItem.bpName}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">支付金额</Text>
                      <Text variant="h6" weight="semibold" color="primary">
                        {formatAmount(selectedItem.wrbtr)}
                      </Text>
                    </View>
                    {selectedItem.payTime && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">支付时间</Text>
                        <Text variant="body1">{selectedItem.payTime}</Text>
                      </View>
                    )}
                    {selectedItem.payType && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">支付类型</Text>
                        <Text variant="body1">{selectedItem.payType}</Text>
                      </View>
                    )}
                    {selectedItem.belnr && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">凭证编号</Text>
                        <Text variant="body1">{selectedItem.belnr}</Text>
                      </View>
                    )}
                    {selectedItem.companyCode && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">门店编码</Text>
                        <Text variant="body1">{selectedItem.companyCode}</Text>
                      </View>
                    )}
                    {selectedItem.compName && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">门店名称</Text>
                        <Text variant="body1">{selectedItem.compName}</Text>
                      </View>
                    )}
                    {selectedItem.merOrderNumber && (
                      <View style={styles.detailRow}>
                        <Text variant="body2" color="secondary">商户支付订单号</Text>
                        <Text variant="body1">{selectedItem.merOrderNumber}</Text>
                      </View>
                    )}
                  </View>
                )}
              </View>
            </View>
          )}
        </Modal>

        {/* 日期选择器 - 跨平台适配 */}
        {showDatePicker && Platform.OS === 'ios' && (
          <Modal
            transparent={true}
            animationType="slide"
            visible={showDatePicker}
            onRequestClose={() => setShowDatePicker(false)}
          >
            <View style={styles.datePickerModal}>
              <View style={styles.datePickerContainer}>
                <View style={styles.datePickerHeader}>
                  <TouchableOpacity
                    onPress={cancelDateSelection}
                    style={styles.datePickerButton}
                  >
                    <Text style={styles.datePickerButtonText}>取消</Text>
                  </TouchableOpacity>
                  <Text style={styles.datePickerTitle}>选择日期</Text>
                  <TouchableOpacity
                    onPress={confirmDateSelection}
                    style={styles.datePickerButton}
                  >
                    <Text style={{...styles.datePickerButtonText, color: '#007AFF', fontWeight: '600'}}>确定</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.datePickerWrapper}>
                  <DateTimePicker
                    value={tempDate}
                    mode="date"
                    display="spinner"
                    onChange={handleDateChange}
                    locale="zh-CN"
                    style={styles.datePicker}
                  />
                </View>
              </View>
            </View>
          </Modal>
        )}

        {/* Android 日期选择器 - 使用原生对话框 */}
        {showDatePicker && Platform.OS === 'android' && (
          <DateTimePicker
            value={new Date(queryParams.startDate || new Date().toISOString().split('T')[0])}
            mode="date"
            display="default"
            onChange={handleDateChange}
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
  },
  queryCard: {
    marginBottom: 12,
  },
  summaryCard: {
    marginBottom: 12,
  },
  listCard: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  queryRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  dateInput: {
    flex: 1,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    height: 56,
  },
  dateInputText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  queryButton: {
    width: 88,
    height: 56,
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    alignSelf: 'flex-end',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  listHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listContent: {
    padding: 12,
  },
  listItem: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  customerCode: {
    marginTop: 4,
  },
  itemAmount: {
    alignItems: 'flex-end',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
  },
  // 加载状态样式
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  // iOS 简洁风格
  loadingContentIOS: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  loadingTextIOS: {
    fontSize: 17,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 12,
  },
  // Android Material Design 风格
  loadingContentAndroid: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingVertical: 24,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
  },
  loadingTextAndroid: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 16,
  },
  loadingSpinner: {
    marginBottom: 0,
  },
  // 空状态样式
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    paddingHorizontal: 20,
  },
  emptyText: {
    marginTop: 12,
    textAlign: 'center',
  },
  emptyHint: {
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.7,
  },
  // Android Modal 样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    margin: 20,
    minWidth: 300,
    maxWidth: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
  },
  // iOS Bottom Sheet 样式
  bottomSheetOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'transparent',
  },
  bottomSheetBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  bottomSheetContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // 为iPhone底部安全区域留空间
    maxHeight: '80%',
  },
  bottomSheetHandle: {
    width: 36,
    height: 4,
    backgroundColor: '#D1D5DB',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  bottomSheetHeader: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  bottomSheetBody: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f8f8',
  },
  // 日期选择器样式
  datePickerModal: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // 为iPhone底部安全区域留空间
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  datePickerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#666',
  },
  datePickerWrapper: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  datePicker: {
    height: 200,
    backgroundColor: '#fff',
    width: '100%',
    alignSelf: 'center',
  },
});

export default PrepayReportScreen;
