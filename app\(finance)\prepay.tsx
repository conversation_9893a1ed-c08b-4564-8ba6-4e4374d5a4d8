/**
 * 预收款页面
 * 客户预收款信息录入和管理
 */

import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { Alert, Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { Card, Text } from '../../src/components/ui';
import { useTheme } from '../../src/stores/appStore';
import { PrepayService } from '../../src/services/prepayService';
import {
  StoreInfo,
  BusinessPartner,
  PrepayFormData,
  FormErrors,
  QRCodeInfo
} from '../../src/types/prepay';
import StorePicker from '../../src/components/prepay/StorePicker';
import BPSelectionModal from '../../src/components/prepay/BPSelectionModal';
import CreateBPModal from '../../src/components/prepay/CreateBPModal';
import QRCodeModal from '../../src/components/prepay/QRCodeModal';

// 改进的单选按钮组组件
interface RadioGroupProps {
  options: { label: string; value: string }[];
  selectedValue: string;
  onValueChange: (value: string) => void;
  compact?: boolean; // 新增紧凑模式
  theme?: any; // 添加主题参数
}

const RadioGroup: React.FC<RadioGroupProps> = ({ options, selectedValue, onValueChange, compact = false, theme }) => {
  return (
    <View style={compact ? getCompactRadioGroupStyle() : getRadioGroupStyle()}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            compact ? getCompactRadioOptionStyle(theme) : getRadioOptionStyle(theme),
            selectedValue === option.value && (compact ? getCompactRadioOptionSelectedStyle(theme) : getRadioOptionSelectedStyle(theme))
          ]}
          onPress={() => onValueChange(option.value)}
          activeOpacity={0.7}
        >
          <Text
            variant="body2"
            style={{
              ...(compact ? getCompactRadioTextStyle(theme) : getRadioTextStyle(theme)),
              ...(selectedValue === option.value ? getRadioTextSelectedStyle() : {})
            }}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// 输入过滤函数
const filterPhoneInput = (text: string): string => {
  // 只允许数字、空格、+、-、()等电话号码字符
  return text.replace(/[^0-9\s+\-()]/g, '');
};

const filterNumericInput = (text: string): string => {
  // 只允许数字和一个小数点
  const filtered = text.replace(/[^0-9.]/g, '');
  // 确保只有一个小数点
  const parts = filtered.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }
  return filtered;
};

const PrepayScreen: React.FC = () => {
  const { theme } = useTheme();

  // 表单数据
  const [formData, setFormData] = useState<PrepayFormData>({
    customerName: '',
    title: '0001', // 0001: 女士, 0002: 先生, 0003: 公司
    phone: '',
    store: '',
    profitCenter: '05', // 默认选择"二手车(服务)"
    amount: '',
  });

  // 表单错误
  const [formErrors, setFormErrors] = useState<FormErrors>({});

  // 数据状态
  const [storeList, setStoreList] = useState<StoreInfo[]>([]);
  const [bpList, setBpList] = useState<BusinessPartner[]>([]);
  const [selectedStore, setSelectedStore] = useState<StoreInfo | null>(null);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // UI状态
  const [showBPSelection, setShowBPSelection] = useState(false);
  const [showCreateBPConfirm, setShowCreateBPConfirm] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [selectedBPIndex, setSelectedBPIndex] = useState(-1);

  // 二维码相关
  const [qrCodeData, setQrCodeData] = useState('');
  const [qrCodeInfo, setQrCodeInfo] = useState<QRCodeInfo>({
    consultant: '',
    name: '',
    money: '',
    code: '',
  });
  const [qrCodeLoading, setQrCodeLoading] = useState(false);

  // 称谓选项
  const titleOptions = [
    { label: '女士', value: '0001' },
    { label: '先生', value: '0002' },
    { label: '公司', value: '0003' },
  ];

  // 利润中心选项
  const profitCenterOptions = [
    { label: '整车销售', value: '01' },
    { label: '售后服务', value: '02' },
    { label: '装饰', value: '03' },
    { label: '二手车(购销)', value: '04' },
    { label: '二手车(服务)', value: '05' },
  ];

  // 页面加载时获取门店列表
  useEffect(() => {
    loadStoreList();
  }, []);

  // 获取门店列表
  const loadStoreList = async () => {
    try {
      setIsLoading(true);
      const stores = await PrepayService.getStoreList();
      setStoreList(stores);

      // 如果只有一个门店，默认选择
      if (stores.length === 1) {
        const defaultStore = stores[0];
        setSelectedStore(defaultStore);
        setFormData(prev => ({
          ...prev,
          store: PrepayService.getStoreLabel(defaultStore),
          // 如果利润中心需要选择（后两位是'99'），默认选择整车销售('01')
          profitCenter: defaultStore.profitCenter.slice(-2) === '99' ? '01' : '05'
        }));
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '获取门店列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    let filteredValue = value;

    // 根据字段类型应用不同的过滤器
    switch (field) {
      case 'phone':
        filteredValue = filterPhoneInput(value);
        break;
      case 'amount':
        filteredValue = filterNumericInput(value);
        break;
      default:
        // 其他字段不过滤
        break;
    }

    setFormData(prev => ({ ...prev, [field]: filteredValue }));

    // 清除对应字段的错误
    if (field in formErrors && formErrors[field as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [field as keyof FormErrors]: undefined }));
    }
  };

  // 增强的表单验证（参考Vue项目的验证规则）
  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // 客户名称验证
    if (!formData.customerName.trim()) {
      errors.customerName = '客户名称不能为空';
    } else {
      const name = formData.customerName.trim();
      if (formData.title !== '0003') {
        // 个人姓名规则
        const personalNameRegex = /^[a-zA-Z·.\u4e00-\u9fa5]{2,}$/;
        if (!personalNameRegex.test(name)) {
          errors.customerName = '姓名不合法';
        } else if (name.charAt(0) === '.' || name.charAt(0) === '·' || name.charAt(0) === ' ' ||
                   name.charAt(name.length - 1) === '.' || name.charAt(name.length - 1) === '·' || name.charAt(name.length - 1) === ' ') {
          errors.customerName = '姓名不合法';
        }
      } else {
        // 公司名称规则
        const companyNameRegex = /^[\u4e00-\u9fa5a-zA-Z·.（）]{5,}$/;
        if (!companyNameRegex.test(name)) {
          errors.customerName = '请输入正确的公司名称';
        } else if (name.charAt(0) === '.' || name.charAt(0) === '·' || name.charAt(0) === ' ' ||
                   name.charAt(name.length - 1) === '.' || name.charAt(name.length - 1) === '·' || name.charAt(name.length - 1) === ' ') {
          errors.customerName = '请输入正确的公司名称';
        }
      }
    }

    // 电话验证
    if (!formData.phone.trim()) {
      errors.phone = '联系电话不能为空';
    } else {
      const phone = formData.phone.trim();
      if (formData.title !== '0003') {
        // 个人只允许手机号
        const mobileRegex = /^1[3456789]\d{9}$/;
        if (!mobileRegex.test(phone)) {
          errors.phone = '请输入正确的电话';
        }
      } else {
        // 公司电话规则
        const companyPhoneRegex = /^[0-9]{7,20}$/;
        if (!companyPhoneRegex.test(phone)) {
          errors.phone = '请输入正确的电话';
        }
      }
    }

    // 门店验证
    if (!selectedStore) {
      errors.store = '请选择门店';
    }

    // 金额验证
    if (!formData.amount.trim()) {
      errors.amount = '预收定金不能为空';
    } else {
      const amountRegex = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
      if (!amountRegex.test(formData.amount)) {
        errors.amount = '请输入正确的金额！';
      } else if (parseFloat(formData.amount) > 50000) {
        errors.amount = '单日交易限额五万！';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 门店选择处理（对应Vue版本的setPrctr函数）
  const handleStoreSelect = (store: StoreInfo) => {
    setSelectedStore(store);
    setFormData(prev => ({
      ...prev,
      store: PrepayService.getStoreLabel(store),
      // 如果利润中心需要选择（后两位是'99'），默认选择整车销售('01')
      // 否则保持默认的'05'
      profitCenter: store.profitCenter.slice(-2) === '99' ? '01' : '05'
    }));

    // 清除门店相关错误
    if (formErrors.store) {
      setFormErrors(prev => ({ ...prev, store: undefined }));
    }
  };

  // 查询业务伙伴
  const queryBusinessPartners = async () => {
    if (!selectedStore) return;

    try {
      const requestData = {
        name1: formData.customerName,
        title: formData.title,
        telf1: formData.phone,
        bukrs: selectedStore.compCode,
        bukrss: selectedStore.compCode + selectedStore.profitCenter,
        money: formData.amount,
        kunnr: '',
        profit: formData.profitCenter,
      };

      const partners = await PrepayService.getBusinessPartnerList(requestData);
      setBpList(partners);



      if (partners.length === 0) {
        // 无BP，显示创建确认对话框
        setShowCreateBPConfirm(true);
      } else if (partners.length === 1 && !partners[0].kunnr) {
        // 存在BP但无客户编号，显示创建确认对话框
        setShowCreateBPConfirm(true);
      } else {
        // 查找匹配的BP（完全按照Vue版本逻辑）
        const matchedBP = partners.find(bp => {
          // Vue版本使用 ruleForm.bukrs（门店公司代码）来判断个人/公司
          // 这里保持与Vue版本完全一致
          if (selectedStore.compCode !== '0003') {
            // 门店公司代码不是'0003'时，按个人用户处理：需要姓名、电话、公司一致
            return bp.telf1 === formData.phone &&
                   bp.name1 === formData.customerName &&
                   bp.bukrs === selectedStore.compCode;
          } else {
            // 门店公司代码是'0003'时，按公司处理：只需要名字一致
            return bp.name1 === formData.customerName;
          }
        });

        if (matchedBP) {
          // 找到匹配的BP，直接创建预付款
          await createPrepayment({
            kunnr: matchedBP.kunnr,
            name1: matchedBP.name1,
          });
        } else {
          // 没有匹配的BP，显示选择对话框
          setSelectedBPIndex(-1);
          setShowBPSelection(true);
        }
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '查询业务伙伴失败');
    }
  };

  // 创建业务伙伴
  const createBusinessPartner = async () => {
    if (!selectedStore) return;

    try {
      const requestData = {
        name1: formData.customerName,
        title: formData.title,
        telf1: formData.phone,
        bukrs: selectedStore.compCode,
        bukrss: selectedStore.compCode + selectedStore.profitCenter,
        money: formData.amount,
        kunnr: '',
        profit: formData.profitCenter,
      };

      const result = await PrepayService.createBusinessPartner(requestData);

      if (result.msg === 'BP创建成功' || result.msg === '已经存在的BP/视图已扩展') {
        // 如果是公司且需要创建，添加到BP关键字日志
        if (formData.title === '0003') {
          await PrepayService.addBpKeywordLog({ bpName: formData.customerName });
        }

        // 创建预付款
        const bpInfo = {
          kunnr: result.kunnr,
          name1: result.name1,
        };
        await createPrepayment(bpInfo);
      } else {
        Alert.alert('错误', result.msg || 'BP创建失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '创建业务伙伴失败');
    }
  };

  // 创建预付款
  const createPrepayment = async (bpInfo: { kunnr: string; name1: string }) => {
    if (!selectedStore) return;

    try {
      const requestData = {
        name1: formData.customerName,
        title: formData.title,
        telf1: formData.phone,
        bukrs: selectedStore.compCode,
        bukrss: selectedStore.compCode + selectedStore.profitCenter,
        money: formData.amount,
        kunnr: bpInfo.kunnr,
        profit: formData.profitCenter,
      };

      const prepayRequest = PrepayService.buildPrepayRequest(requestData, selectedStore, bpInfo);
      const result = await PrepayService.createPrepay(prepayRequest);

      if (result.id !== '0') {
        // 预付款创建成功，获取二维码
        await generateQRCode(result.id);
      } else {
        Alert.alert('错误', result.msg || '预付款创建失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '创建预付款失败');
    }
  };

  // 生成二维码
  const generateQRCode = async (prepayId: string) => {
    try {
      setQrCodeLoading(true);
      setShowQRCode(true);

      const qrData = await PrepayService.getQrCode(prepayId);
      setQrCodeData(qrData);

      // 设置二维码信息
      setQrCodeInfo({
        consultant: 'Admin', // 这里可以从用户信息中获取
        name: formData.customerName,
        money: formData.amount,
        code: selectedStore?.compName || '',
      });

      // 重置表单
      handleClear();
    } catch (error: any) {
      Alert.alert('错误', error.message || '获取二维码失败');
    } finally {
      setQrCodeLoading(false);
    }
  };

  // 清除表单
  const handleClear = () => {
    setFormData({
      customerName: '',
      title: '0001',
      phone: '',
      store: '',
      profitCenter: '05',
      amount: '',
    });
    setFormErrors({});
    setSelectedStore(null);
    setBpList([]);
    setSelectedBPIndex(-1);
  };

  // 主要提交处理
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 防重复提交
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      // 查询业务伙伴并处理后续流程
      await queryBusinessPartners();
    } catch (error: any) {
      Alert.alert('提交失败', error.message || '请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // BP选择确认（完全按照Vue版本逻辑）
  const handleBPSelectionConfirm = async () => {
    if (selectedBPIndex < 0 || selectedBPIndex >= bpList.length) return;

    const selectedBP = bpList[selectedBPIndex];
    setShowBPSelection(false);

    // 按照Vue逻辑：当列表选择后bukrs不一致时要进行bp创建
    if (selectedStore && selectedStore.compCode !== selectedBP.bukrs) {
      // 更新表单数据为选中的BP信息（对应Vue中的ruleForm更新）
      setFormData(prev => ({
        ...prev,
        customerName: selectedBP.name1,
        phone: selectedBP.telf1,
        title: selectedBP.title,
      }));
      // 显示创建BP确认对话框
      setShowCreateBPConfirm(true);
    } else {

      // 公司代码一致，直接创建预付款
      await createPrepayment({
        kunnr: selectedBP.kunnr,
        name1: selectedBP.name1,
      });
    }
  };

  // 关键字验证和创建BP确认
  const handleCreateBPConfirm = async () => {
    setShowCreateBPConfirm(false);

    try {
      // 进行关键字验证
      const isValidKeyword = await PrepayService.validateKeyword({
        requiredKeyword: formData.customerName
      });

      if (formData.title === '0003' && !isValidKeyword) {
        // 公司名称需要确认
        Alert.alert(
          '警告',
          '请确认是否为公司全称?',
          [
            { text: '取消', style: 'cancel' },
            { text: '确认', onPress: () => createBusinessPartner() }
          ]
        );
      } else if (formData.title !== '0003' && isValidKeyword && formData.customerName.length > 4) {
        // 个人名称错误
        Alert.alert('警告', '称谓错误/联系系统管理员');
      } else {
        // 直接创建BP
        await createBusinessPartner();
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '验证失败');
    }
  };

  // 返回按钮
  const renderBackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={styles.backButton}
    >
      <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: '预收款',
          headerShown: true,
          headerLeft: renderBackButton,
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTitleStyle: {
            color: theme.colors.text.primary,
          },
        }}
      />
      
      <View style={[styles.mainContainer, { backgroundColor: theme.colors.background.secondary }]}>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.contentPadding}>
            {/* 单一卡片容器 */}
            <Card
              padding={12}
              elevation={Platform.OS === 'android' ? 2 : 1}
              style={styles.singleCard}
            >
              <Text variant="h6" weight="semibold" style={[styles.sectionTitle, { color: theme.colors.text.primary }] as any}>
                客户信息
              </Text>

              {/* 客户名称 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>客户名称 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.customerName}
                  onChangeText={(value) => handleInputChange('customerName', value)}
                  placeholder="请输入客户名称"
                  placeholderTextColor={theme.colors.text.tertiary}
                  style={getInputStyle(theme)}
                />
                {formErrors.customerName && (
                  <Text style={styles.errorText}>{formErrors.customerName}</Text>
                )}
              </View>

              {/* 称谓 */}
              <View style={styles.formItemCompact}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>称谓 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <RadioGroup
                  options={titleOptions}
                  selectedValue={formData.title}
                  onValueChange={(value) => handleInputChange('title', value)}
                  theme={theme}
                />
              </View>

              {/* 联系电话 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>联系电话 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.phone}
                  onChangeText={(value) => handleInputChange('phone', value)}
                  placeholder="请输入联系电话"
                  placeholderTextColor={theme.colors.text.tertiary}
                  keyboardType="phone-pad"
                  style={getInputStyle(theme)}
                />
                {formErrors.phone && (
                  <Text style={styles.errorText}>{formErrors.phone}</Text>
                )}
              </View>

              {/* 门店名称 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>门店名称 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <StorePicker
                  stores={storeList}
                  selectedStore={selectedStore}
                  onStoreSelect={handleStoreSelect}
                  placeholder="请选择门店"
                  error={formErrors.store}
                  theme={theme}
                />
              </View>

              {/* 利润中心 - 只在门店代码末尾为99时显示 */}
              {selectedStore && selectedStore.profitCenter.slice(-2) === '99' && (
                <View style={styles.formItemCompact}>
                  <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>利润中心</Text>
                  <RadioGroup
                    options={profitCenterOptions}
                    selectedValue={formData.profitCenter}
                    onValueChange={(value) => handleInputChange('profitCenter', value)}
                    compact={true}
                    theme={theme}
                  />
                </View>
              )}

              {/* 预收定金 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }] as any}>预收定金 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.amount}
                  onChangeText={(value) => handleInputChange('amount', value)}
                  placeholder="请输入预收定金金额"
                  placeholderTextColor={theme.colors.text.tertiary}
                  keyboardType="numeric"
                  style={getInputStyle(theme)}
                />
                {formErrors.amount && (
                  <Text style={styles.errorText}>{formErrors.amount}</Text>
                )}
              </View>
            </Card>

            {/* 操作按钮 */}
            <View style={styles.buttonContainer}>
              <View style={styles.buttonRow}>
                <TouchableOpacity
                  onPress={handleSubmit}
                  disabled={isSubmitting || isLoading}
                  style={getSubmitButtonStyle(theme)}
                  activeOpacity={0.8}
                >
                  <Text style={getSubmitButtonTextStyle()}>
                    {isSubmitting ? '生成二维码中...' : '生成二维码'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleClear}
                  disabled={isSubmitting || isLoading}
                  style={getClearButtonStyle(theme)}
                  activeOpacity={0.8}
                >
                  <Text style={getClearButtonTextStyle(theme)}>重置</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* BP选择对话框 */}
        <BPSelectionModal
          visible={showBPSelection}
          bpList={bpList}
          selectedIndex={selectedBPIndex}
          onClose={() => setShowBPSelection(false)}
          onConfirm={handleBPSelectionConfirm}
          onCreateNew={() => {
            setShowBPSelection(false);
            setShowCreateBPConfirm(true);
          }}
          onSelectionChange={setSelectedBPIndex}
          theme={theme}
        />

        {/* 创建BP确认对话框 */}
        <CreateBPModal
          visible={showCreateBPConfirm}
          onClose={() => setShowCreateBPConfirm(false)}
          onConfirm={handleCreateBPConfirm}
          theme={theme}
        />

        {/* 二维码显示对话框 */}
        <QRCodeModal
          visible={showQRCode}
          qrCodeData={qrCodeData}
          qrCodeInfo={qrCodeInfo}
          isLoading={qrCodeLoading}
          onClose={() => setShowQRCode(false)}
          theme={theme}
        />
      </View>
    </>
  );
};

// 样式函数 - 简单的单层输入框样式
const getInputStyle = (theme: any) => ({
  borderRadius: 8,
  minHeight: 48,
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderWidth: 2,
  borderColor: theme.colors.border.primary,
  backgroundColor: theme.colors.surface.primary,
  fontSize: 16,
  color: theme.colors.text.primary,
  ...(Platform.OS === 'android' && {
    elevation: 0,
    shadowOpacity: 0,
  }),
});

const getSubmitButtonStyle = (theme: any) => ({
  flex: 1,
  minHeight: 48,
  borderRadius: 8,
  backgroundColor: theme.colors.primary,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getClearButtonStyle = (theme: any) => ({
  flex: 1,
  minHeight: 48,
  borderRadius: 8,
  backgroundColor: theme.colors.surface.secondary,
  borderWidth: 1,
  borderColor: theme.colors.border.primary,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getSubmitButtonTextStyle = () => ({
  fontSize: 16,
  fontWeight: '600' as const,
  color: '#FFFFFF',
});

const getClearButtonTextStyle = (theme: any) => ({
  fontSize: 16,
  fontWeight: '600' as const,
  color: theme.colors.text.primary,
});

const getRadioGroupStyle = () => ({
  flexDirection: 'row' as const,
  flexWrap: 'wrap' as const,
  gap: 8,
  marginTop: 4,
});

const getRadioOptionStyle = (theme: any) => ({
  paddingHorizontal: 16,
  paddingVertical: 8,
  borderRadius: 20,
  borderWidth: 1,
  borderColor: theme?.colors?.border?.primary || '#D1D5DB',
  backgroundColor: theme?.colors?.surface?.primary || '#FFFFFF',
  marginRight: 8,
  marginBottom: 8,
  minWidth: 70,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getRadioOptionSelectedStyle = (theme: any) => ({
  borderColor: theme?.colors?.primary || '#3B82F6',
  backgroundColor: theme?.colors?.primary || '#3B82F6',
});

const getRadioTextStyle = (theme: any) => ({
  fontSize: 14,
  fontWeight: '500' as const,
  color: theme?.colors?.text?.secondary || '#6B7280',
});

const getRadioTextSelectedStyle = () => ({
  color: '#FFFFFF',
  fontWeight: '600' as const,
});

// 紧凑模式样式函数
const getCompactRadioGroupStyle = () => ({
  flexDirection: 'row' as const,
  flexWrap: 'wrap' as const,
  gap: 4,
  marginTop: 4,
});

const getCompactRadioOptionStyle = (theme: any) => ({
  paddingHorizontal: 8,
  paddingVertical: 8,
  borderRadius: 20,
  borderWidth: 1,
  borderColor: theme?.colors?.border?.primary || '#D1D5DB',
  backgroundColor: theme?.colors?.surface?.primary || '#FFFFFF',
  marginRight: 4,
  marginBottom: 4,
  minWidth: 60,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getCompactRadioOptionSelectedStyle = (theme: any) => ({
  borderColor: theme?.colors?.primary || '#3B82F6',
  backgroundColor: theme?.colors?.primary || '#3B82F6',
});

const getCompactRadioTextStyle = (theme: any) => ({
  fontSize: 12,
  fontWeight: '500' as const,
  color: theme?.colors?.text?.secondary || '#6B7280',
});

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    minHeight: '100%',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'android' ? 24 : 16,
  },
  contentPadding: {
    padding: 12,
  },
  backButton: {
    padding: 12,
    borderRadius: 8,
    // 增加触摸区域
    minWidth: 44,
    minHeight: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  singleCard: {
    marginBottom: 16,
    borderRadius: 12,
    // 安卓优化的阴影
    ...(Platform.OS === 'android' && {
      elevation: 2,
    }),
    // iOS优化的阴影
    ...(Platform.OS === 'ios' && {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
    }),
  },
  sectionTitle: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: '600',
    // color will be applied inline with theme
  },
  formItem: {
    marginBottom: 16, // 增加底部间距
    minHeight: 90, // 增加预留错误提示空间高度
    position: 'relative', // 为绝对定位的错误提示添加相对定位容器
  },
  label: {
    marginBottom: 3,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    // color will be applied inline with theme
  },
  buttonContainer: {
    marginTop: 16,
    marginBottom: Platform.OS === 'android' ? 24 : 20,
    gap: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
    minHeight: 20, // 增加固定错误提示高度
    position: 'absolute', // 使用绝对定位避免影响布局
    bottom: -8, // 调整定位位置，使错误提示更贴近输入框
  },
  formItemCompact: {
    marginBottom: 12, // 减少底部间距
    minHeight: 60, // 减少预留空间高度，因为不需要错误提示空间
    position: 'relative',
  },
});

export default PrepayScreen;
